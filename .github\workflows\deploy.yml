name: Node.js CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:

  build:

    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16.x]

    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v1
      with:
        node-version: ${{ matrix.node-version }}
    - run: npm ci
    - run: npm run build --if-present

    - name: SSH Remote Commands
      uses: appleboy/ssh-action@v0.1.2
      with:        # secret keys from github actions
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USER }}
        key: ${{ secrets.KEY }}
        port: ${{ secrets.PORT }}
        script: |

          cd ~/ai-top-fit-backend   # repo directory on the server
          git pull origin main
          npm install
          pm2 restart index   # or pm2 restart server