const {authService, favouriteService, userService} = require('../services');
const catchAsync = require('../utils/catchAsync');
const Notification = require("../models/notification.model");
const UserNotification = require('../models/userNotification.model');




const list = catchAsync(async (req, res) => {

    const user = req.user;
    const notifications = await UserNotification.find({
                                            receiver:user._id,
                                        }).sort({createdAt:-1});
    await UserNotification.updateMany({
        receiver:user._id,
        isRead:false
    },{ $set: { isRead: true } });

    return res.status(200).json({
        status:true,
        notifications,
        msg:""
    });

});

const unReadcount = catchAsync(async (req, res) => {

    const user = req.user;
    const notifications = await UserNotification.find({
        receiver:user._id,
        isRead:false
    }).count();

    return res.status(200).json({
        status:true,
        notifications,
        msg:""
    });

});

module.exports = {
  list,
  unReadcount
};
