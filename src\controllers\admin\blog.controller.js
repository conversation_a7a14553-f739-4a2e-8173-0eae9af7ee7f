const catchAsync = require("../../utils/catchAsync");
const blogService = require("../../services/blog.service");
const { getPaginateConfig } = require("../../utils/queryPHandler");

const getBlogs = catchAsync(async (req, res, next) => {

    const {filters,options} = getPaginateConfig(req.query);

    if(filters.search){
        filters.$or = [
            {title:{$regex:filters.search,$options:"i"}},
            {description:{$regex:filters.search,$options:"i"}}
        ];
        delete filters.search;
    }

    if(filters.timeFrame ){

        let start;
        let end;
        const today = new Date();
            if(filters.timeFrame == "today"){
                start = new Date();
                start.setHours(0, 0, 0, 0);

                end = new Date();
                end.setHours(23, 59, 59, 999);
            }else if(filters.timeFrame == "thisMonth"){
                start = new Date(today.getFullYear(), today.getMonth(), 1);
                start.setHours(0, 0, 0, 0);
                
                end = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                end.setHours(23, 59, 59, 999);
            }else if(filters.timeFrame == "thisWeek"){
                start = new Date(today.setDate(today.getDate() - today.getDay()));
                start.setHours(0, 0, 0, 0);

                end = new Date(start);
                end.setDate(end.getDate() + 6);
                end.setHours(23, 59, 59, 999);
            }else if(filters.timeFrame == "thisYear"){
                start = new Date(today.getFullYear(), 0, 1);
                start.setHours(0, 0, 0, 0);
                
                end = new Date(today.getFullYear(), 11, 31);
                end.setHours(23, 59, 59, 999);
            }

            if(filters.timeFrame != "all"){
                filters.createdAt = { $gte: start, $lte: end }
            }

            delete filters.timeFrame;
        }

    const blogs = await blogService.getBlogs(filters,options);
    res.status(200).send({ data: blogs, message: '' });

});

const getBlog = catchAsync(async (req, res, next) => {

    const blog = await blogService.getBlogById(req.params.id);
    res.status(200).send({ data: blog, message: '' });

});

const store = catchAsync(async (req, res, next) => {

    const blog = await blogService.createBlog(req.body, req.file);
    res.status(201).send({ data: blog, message: 'Blog is created Successfully' });

});

const updateBlog = catchAsync(async (req, res, next) => {

    const blog = await blogService.updateBlogById(req.body._id, req.body, req.files);
    res.status(200).send({ data: blog, message: 'Blog is updated Successfully' });

});

const deleteBlog = catchAsync(async (req, res, next) => {

    const blog = await blogService.deleteBlogById(req.params.id);
    res.status(200).send({ data: blog, message: 'Blog is deleted Successfully' });

});

module.exports = {
    store,
    getBlog,
    getBlogs,
    updateBlog,
    deleteBlog
}