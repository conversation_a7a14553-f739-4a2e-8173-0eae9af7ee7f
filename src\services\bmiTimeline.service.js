const { User, Client } = require('../models');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { fileUploadService } = require('../microservices');
const { BmiTimeline } = require('../models/bmiTimeline.model');


async function getBmiTimelineById(id) {
    const bmiTimeline = await BmiTimeline.findById(id).populate("userId","_id name profilePic");
    return bmiTimeline;
}

async function getBmiTimelines(filter,orders) {
    // options.populate = [
    //     "userId::_id,name,profilePic"
    // ];
    return await BmiTimeline.find(filter).sort(orders);

}

async function createBmiTimeline(details, image) {
    let data = { ...details };

    if (image) {
        const [thumbnail] = await fileUploadService.s3Upload([image], 'thumbnail').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload thumbnail');
        })
        data = { ...data, image: thumbnail };
    };

    return await BmiTimeline.create(data);
}

async function updateBmiTimelineById(id, body, image) {
    const bmiTimeline = await BmiTimeline.findById(id);
    let updates = { ...body };

    if (image) {
        const [thumbnail] = await fileUploadService.s3Upload([image], 'thumbnail').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload thumbnail');
        });
        if (bmiTimeline.image) {
            const oldPicKey = bmiTimeline.thumbnail.key;
            await fileUploadService
                .s3Delete(oldPicKey)
                .catch(err => console.log('Failed to delete image', oldPicKey));
        }
        updates = { ...updates, image: thumbnail };
    }

    return await BmiTimeline.findByIdAndUpdate(id, updates, { new: true });
}

async function deleteBmiTimelineById(id) {
    try {
        await BmiTimeline.findByIdAndDelete(id);
        return true;
    } catch (err) {
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to delete the Blog');
    }
}

module.exports = {
    getBmiTimelines,
    getBmiTimelineById,
    createBmiTimeline,
    updateBmiTimelineById,
    deleteBmiTimelineById
}