const mongoose = require('mongoose');
const {paginate} = require('./plugins/paginate');

const workoutSchema = new mongoose.Schema(
  {
    video: {
      type: {
        key: String,
        url: String,
      },
      default: null,
    },
    category:{
      type:String,
      default: null
    },
    name: {
      type: String,
      default: null,
    },
    description: {
        type: String,
        default: null,
    },
  },
  {timestamps: true}
);

workoutSchema.plugin(paginate);

const Workout = mongoose.model('Workout', workoutSchema);
module.exports = {
    Workout
};
