const mongoose = require('mongoose');
const { paginate } = require('./plugins/paginate');

const coinOfferSchema = new mongoose.Schema(
    {
        title: {
            type: String,
            default: null,
        },
        coinNumber: {
            type: Number,
            default: null,
        },
        price: {
            type: Number,
            default: null,
        },
        totalSold: {
            type: Number,
            default: 0,
        },
        isActive: {
            type: Boolean,
            default: true
        },
    },
    { timestamps: true }
);

coinOfferSchema.plugin(paginate);

const CoinOffer = mongoose.model('CoinOffer', coinOfferSchema);
module.exports = {
    CoinOffer
};
