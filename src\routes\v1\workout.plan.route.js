const express = require("express");
const router = express.Router();

const { workoutPlanController, aiController } = require("../../controllers");
const { firebaseAuth } = require("../../middlewares/firebaseAuth");

router.get("/", firebaseAuth, workoutPlanController.getAllWorkoutPlan);
router.post("/start", firebaseAuth, workoutPlanController.startWorkout);
router.post("/end", firebaseAuth, workoutPlanController.endWorkout);
router.get("/history", firebaseAuth, workoutPlanController.workoutHistory);
router.get("/calender", firebaseAuth, workoutPlanController.calender);
router.get("/workoutByDay", firebaseAuth, workoutPlanController.workoutByDate);

router.get("/:id", workoutPlanController.getWorkoutPlanById);

module.exports = router;
