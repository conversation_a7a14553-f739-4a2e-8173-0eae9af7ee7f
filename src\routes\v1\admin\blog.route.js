const express = require('express');
const router = express.Router();

const validate = require('../../../middlewares/validate');
const { adminProtect } = require('../../../middlewares/adminAuth');
const blogValidation = require('../../../validations/blog.validation');
const searchValidation = require('../../../validations/search.validation');

const blogController = require('../../../controllers/admin/blog.controller');
const { fileUploadService } = require('../../../microservices');


router.post(
    '/add',
    adminProtect,
    fileUploadService.multerUpload.single('thumbnail'),
    validate(blogValidation.AddBlog),
    blogController.store
);

router.post(
    '/update',
    adminProtect,
    fileUploadService.multerUpload.single('thumbnail'),
    validate(blogValidation.EditBlog),
    blogController.updateBlog
)

router.get(
    '/list',
    adminProtect,
    validate(searchValidation.query),
    blogController.getBlogs
);
router.get(
    '/:id',
    adminProtect,
    blogController.getBlog
);

router.delete(
    '/:id',
    adminProtect,
    blogController.deleteBlog
);

module.exports = router;
