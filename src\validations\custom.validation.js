const Joi = require('joi');
const {default: mongoose} = require('mongoose');

const dbOptionsSchema = {
  limit: Joi.number().default(10),
  page: Joi.number().default(1),
  sortBy: Joi.string().default('createdAt'),
  sortOrder: Joi.string()
    .valid('', 'asc')
    .default(''),
};

const objectId = (value, helpers) => {
  if (!value.match(/^[0-9a-fA-F]{24}$/)) {
    return helpers.message('"{{#label}}" must be a valid id');
  }
  return new mongoose.Types.ObjectId(value);
};

const parseStringToObject = (value, helpers) => {
  try {
    const parsedValue = JSON.parse(value);
    if (typeof parsedValue === 'object' && parsedValue !== null) {
      return parsedValue;
    }
    return helpers.message('{{#label}} must be a valid JSON object');
  } catch (error) {
    return helpers.message('{{#label}} must be a valid JSON object');
  }
};

const convertFieldToRegEx = (value, helpers) => {
  if (value === '') {
    return helpers.message('{{#label}} cannot be empty');
  }
  return new RegExp(value, 'i');
};

const validateObjectBySchema = schema => (value, helpers) => {
  const {value: validatedValue, error} = Joi.compile(schema)
    .prefs({errors: {label: 'key'}})
    .validate(value);
  if (error) {
    return helpers.message(error.details.map(details => details.message).join(', '));
  }
  return validatedValue;
};

// convert comma separated values to array of strings
const convertCSVToArray = (value, helpers) => {
  const arr = value.split(',');
  const arrValidated = arr.every(val => Joi.string().validate(val).error === undefined);
  return arrValidated ? arr : helpers.error('Invalid comma separated values');
};

const convertCSVToObjectIdArray = (value, helpers) => {
  const arr = value.split(',');
  const arrValidated = arr.every(
    val =>
      Joi.string()
        .custom(objectId)
        .validate(val).error === undefined
  );
  return arrValidated ? arr : helpers.error('Invalid');
};

async function isTextURL(text) {
  const schema = Joi.string().uri();
  const {error} = schema.validate(text);
  return !error;
}

const validateMediaExtension = (value, helpers, mediaType) => {
  let allowedExtensions = [];
  
  // Define allowed extensions based on media type
  if (mediaType === 'image') {
    allowedExtensions = ['jpg', 'jpeg', 'png'];
  } else if (mediaType === 'video') {
    allowedExtensions = ['mp4', 'mov', 'avi', 'wav'];
  } else {
    return helpers.error('any.invalid');
  }

  // Validate the file extension
  const extension = value.split('.').pop().toLowerCase();
  if (!allowedExtensions.includes(extension)) {
    return helpers.error('any.invalid');
  }

  return value;
};

const validateMaxWords = (value, helpers, maxWords) => {
  const wordCount = value.trim().split(/\s+/).length;
  if (wordCount > maxWords) {
    return helpers.error('any.invalid');
  }
  return value;
};

const validateSpecialChar = (value, helpers, maxWords) => {
  // Define a regular expression to match special characters
  const regex = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/;

  // Test the string against the regular expression
  if(regex.test(value)){
    return helpers.error('any.invalid');
  }
  return value;
}

module.exports = {
  objectId,
  isTextURL,
  dbOptionsSchema,
  convertCSVToArray,
  parseStringToObject,
  convertFieldToRegEx,
  convertCSVToObjectIdArray,
  validateObjectBySchema,
  validateMediaExtension,
  validateMaxWords,
  validateSpecialChar
};
