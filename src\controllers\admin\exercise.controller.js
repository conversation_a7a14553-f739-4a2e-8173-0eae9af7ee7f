const catchAsync = require("../../utils/catchAsync");
const exerciseService = require("../../services/exercise.service");

const getExercises = catchAsync(async (req, res, next) => {
    try {
        const { sortOrder, keyword, timeFrame, limit, page } = req.query;
        const exercises = await exerciseService.getExercises(sortOrder, keyword, timeFrame, { limit, page, sortOrder, sortBy: "createdAt" });
        res.status(200).send({ data: exercises, message: '' });
    } catch (error) {
        next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing exercises', error));
    }
});

const getExercise = catchAsync(async (req, res, next) => {

    const exercise = await exerciseService.getExerciseById(req.params.id);
    res.status(200).send({ data: exercise, message: '' });

});

const store = catchAsync(async (req, res, next) => {

    const exercise = await exerciseService.createExercise(req.body, req.files);
    res.status(201).send({ data: exercise, message: 'Exercise is created Successfully' });

});

const updateExercise = catchAsync(async (req, res, next) => {

    const exercise = await exerciseService.updateExerciseById(req.body._id, req.body, req.files);
    res.status(200).send({ data: exercise, message: 'Exercise is updated Successfully' });

});

const deleteExercise = catchAsync(async (req, res, next) => {

    const exercise = await exerciseService.deleteExericeById(req.params.id);
    res.status(200).send({ data: exercise, message: 'Exercise is deleted Successfully' });

});

module.exports = {
    store,
    getExercise,
    getExercises,
    updateExercise,
    deleteExercise
}

