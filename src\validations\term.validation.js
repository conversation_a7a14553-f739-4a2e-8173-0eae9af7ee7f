const Joi = require('joi');
const { objectId } = require('./custom.validation');

const termSchema = {
    title: Joi.string(),
    body: Joi.string()
};


const AddTerm = {
    body: Joi.object().keys({
        ...termSchema,
    }),
};

const EditTerm = {
    body: Joi.object().keys({
        _id: Joi.string()
            .custom(objectId),
        ...termSchema,
    }),
};

module.exports = {
    AddTerm,
    EditTerm
};
