const config = require('../config/config');
const {User} = require('../models/user.model');
const stripe = require('stripe')(config.stripe.secret);

async function createCustomer(userId, name, email) {
  const customer = await stripe.customers.create({
    name,
    email,
  });
  await User.findByIdAndUpdate(userId,{stripeCustomerId:customer.id})
  return customer.id;
}

module.exports = {
  createCustomer,
};