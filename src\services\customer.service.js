const config = require("../config/config");
const { User } = require("../models/user.model");
const stripe = require("stripe")(config.stripe.secret);
const eventEmitter = require("../events/eventEmitter");
const { emitterEventNames } = require("../constants");

async function createCustomer(userId, name, email) {
  const customer = await stripe.customers.create({
    name,
    email,
  });
  const updatedUser = await User.findByIdAndUpdate(
    userId,
    { stripeCustomerId: customer.id },
    { new: true }
  );

  // Emit event for assistant update
  if (updatedUser) {
    eventEmitter.emit(emitterEventNames.ASSISTANT_UPDATE, updatedUser);
  }

  return customer.id;
}

module.exports = {
  createCustomer,
};
