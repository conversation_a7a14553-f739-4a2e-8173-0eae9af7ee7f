const express = require('express');
const router = express.Router();

const validate = require('../../../middlewares/validate');
const { adminProtect } = require('../../../middlewares/adminAuth');
const dietCategoryValidation = require('../../../validations/dietCategory.validation');

const dietCategoryController = require('../../../controllers/admin/dietCategory.controller');
const { fileUploadService } = require('../../../microservices');
const searchValidation= require('../../../validations/search.validation');


router.post(
    '/add',
    adminProtect,
    fileUploadService.multerUpload.single('thumbnail'),
    validate(dietCategoryValidation.AddDietCategory),
    dietCategoryController.store
);

router.post(
    '/update',
    adminProtect,
    fileUploadService.multerUpload.single('thumbnail'),
    validate(dietCategoryValidation.EditDietCategory),
    dietCategoryController.updateDietCategory
)

router.get(
    '/list',
    adminProtect,
    validate(searchValidation.query),
    dietCategoryController.getDietCategories
);
router.get(
    '/:id',
    adminProtect,
    dietCategoryController.getDietCategory
);

router.delete(
    '/:id',
    adminProtect,
    dietCategoryController.deleteDietCategory
);

module.exports = router;
