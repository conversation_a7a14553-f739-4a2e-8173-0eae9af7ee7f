const express = require('express');
const router = express.Router();

const validate = require('../../../middlewares/validate');
const { adminProtect } = require('../../../middlewares/adminAuth');
const notificationValidation = require('../../../validations/notification.validation');
const searchValidation = require('../../../validations/search.validation');

const NotificationController = require('../../../controllers/admin/notification.controller');

router.get(
    '/list',
    adminProtect,
    validate(searchValidation.query),
    NotificationController.list
);

router.post(
    '/send',
    adminProtect,
    validate(notificationValidation.AddNotification),
    NotificationController.sendNotification
);

router.get(
    '/:id',
    adminProtect,
    NotificationController.detail
);

router.delete(
    '/delete/:id',
    adminProtect,
    NotificationController.deleteNotification
);


module.exports = router;