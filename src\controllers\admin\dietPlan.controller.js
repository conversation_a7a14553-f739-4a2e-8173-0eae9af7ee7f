const catchAsync = require("../../utils/catchAsync");
const dietPlanService = require("../../services/dietPlan.service");
const httpStatus = require('http-status');
const ApiError = require('../../utils/ApiError');
const {fileUploadService} = require("../../microservices");
const {getPaginateConfig} = require('../../utils/queryPHandler');

const addMeasurementUnit = catchAsync(async (req,res,next) => {
    console.log(req.body);
    const addMeasurementUnit = await dietPlanService.addMeasurementUnit(req.body);
    res.status(201).send({status:true, data: addMeasurementUnit, message: 'Measurement unit is created Successfully'});
});

const updateMeasurementUnit = catchAsync(async (req,res,next) => {
    const updateMeasurementUnit = await dietPlanService.updateMeasurementUnit(req.body.id,req.body);
    res.status(200).send({status:true, data: updateMeasurementUnit, message: 'Measurement unit is updated Successfully'});
});

const listMeasurementUnit = catchAsync(async (req,res,next) => {
    const {keyword, timeFrame, ...otherOptions} = req.query;
    const {options} = getPaginateConfig(otherOptions);
    const listMeasurementUnit = await dietPlanService.listMeasurementUnit(keyword, timeFrame, options);
    res.status(200).send({status:true, data: listMeasurementUnit, message: ''});
});

const addIngredient = async (req, res, next) => {
    try {
      console.log(req.body);
      const newIngredient = await dietPlanService.addIngredient(req.body,req.file);
      res.status(httpStatus.CREATED).json({ status:true, data: newIngredient, message: 'Ingredient added successfully' });
    } catch (error) {
      next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error adding ingredient', error));
    }
  };
  
  const updateIngredient = async (req, res, next) => {
    try {
      const updatedIngredient = await dietPlanService.updateIngredient(req.body.id, req.body,req.file);
      res.status(httpStatus.OK).json({ status:true, data: updatedIngredient, message: 'Ingredient updated successfully' });
    } catch (error) {
      next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error updating ingredient', error));
    }
  };
  
  const listIngredients = async (req, res, next) => {
    try {
      const {keyword, timeFrame, ...otherOptions} = req.query;
      const {options} = getPaginateConfig(otherOptions);
      const ingredients = await dietPlanService.listIngredients(keyword, timeFrame, options);
      res.status(httpStatus.OK).json({ status:true, data: ingredients });
    } catch (error) {
      next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing ingredients', error));
    }
  };
  
  const deleteIngredient = async (req, res, next) => {
    try {
      await dietPlanService.deleteIngredient(req.params.id);
      res.status(httpStatus.OK).json({  status:true, message: 'Ingredient deleted successfully' });
    } catch (error) {
        next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error deleting ingredient', error));
    }
};

const addRecipe = async (req, res, next) => {
    try {
      const newRecipe = await dietPlanService.addRecipe(req.body);
      res.status(httpStatus.CREATED).json({ status:true, data: newRecipe, message: 'Recipe added successfully' });
    } catch (error) {
      next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error adding recipe', error));
    }
  };
  
  const updateRecipe = async (req, res, next) => {
    try {
      const updateData = { ...req.body }; // Copy the req.body to avoid modifying it directly
      const updatedRecipe = await dietPlanService.updateRecipe(req.body.id, updateData);
      res.status(httpStatus.OK).json({ status:true, data: updatedRecipe, message: 'Recipe updated successfully' });
    } catch (error) {
      next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error updating recipe', error));
    }
  };

  const updateMediaForRecipe = async (req, res, next) => {
    try {
      const { id }= req.body; // Copy the req.body to avoid modifying it directly
      if (req.files) {
        if (req.files['thumbnail']) {
          const thumbnailUpload = req.files['thumbnail'][0];
          const thumbnailUrl = await fileUploadService.s3Upload([thumbnailUpload], 'thumbnails');
          req.body.thumbnail = thumbnailUrl ? thumbnailUrl[0].url : req.body.thumbnail;
        }
        
        if (req.files['recipeVideo']) {
          const recipeVideoUpload = req.files['recipeVideo'][0];
          const recipeVideoUrl = await fileUploadService.s3Upload([recipeVideoUpload], 'videos');
          req.body.recipeVideo = recipeVideoUrl ? recipeVideoUrl[0].url : req.body.recipeVideo;
        }
      }
      const updatedRecipe = await dietPlanService.updateRecipe(id, req.body);
      res.status(httpStatus.OK).json({ status:true, data: updatedRecipe, message: 'Recipe updated successfully' });
    } catch (error) {
      next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error updating recipe', error));
    }
  };
  
  const listRecipes = async (req, res, next) => {
    try {
      const {keyword, timeFrame, ...otherOptions} = req.query;
      const {options} = getPaginateConfig(otherOptions);
      const recipes = await dietPlanService.listRecipes(keyword, timeFrame, options);
      res.status(httpStatus.OK).json({ status:true, data: recipes });
    } catch (error) {
      next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing recipes', error));
    }
  };
  
  
  const deleteRecipe = async (req, res, next) => {
    try {
      await dietPlanService.deleteRecipe(req.params.id);
      res.status(httpStatus.OK).json({  status:true, message: 'Recipe deleted successfully' });
    } catch (error) {
      next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error deleting recipe', error));
    }
  };

  const deleteMeasurementUnit = async (req, res, next) => {
    try {
      await dietPlanService.deleteMeasurementUnit(req.params.id);
      res.status(httpStatus.OK).json({  status:true, message: 'Measurement unit deleted successfully' });
    } catch (error) {
      next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error deleting Measurement unit', error));
    }
  };

  const addDietPlan = async (req, res, next) => {
    try {
        let { thumbnail, ...recipeData } = req.body;

        if (req.files) {
          const thumbnailUpload = req.files['thumbnail'] ? req.files['thumbnail'][0] : null;
    
          const [thumbnailUrl] = await Promise.all([
            thumbnailUpload ? fileUploadService.s3Upload([thumbnailUpload], 'thumbnails') : Promise.resolve(null)
          ]);
    
          thumbnail = thumbnailUrl ? thumbnailUrl[0].url : thumbnail;
        }
    
        // Include thumbnail and recipeVideo URLs in req.body
        req.body = { ...recipeData, thumbnail };
      const dietPlan = await dietPlanService.addDietPlan(req.body);
      res.status(201).json({ status:true, data: dietPlan, message: 'Diet plan added successfully' });
    } catch (error) {
      // Handle error
      next(error);
    }
  };
  
  const updateDietPlan = async (req, res, next) => {
    try {
      const { ...updateData } = req.body;
      const dietPlan = await dietPlanService.updateDietPlan(req.params.id, updateData);
      res.json({ status:true, data: dietPlan, message: 'Diet plan updated successfully' });
    } catch (error) {
      // Handle error
      next(error);
    }
  };

  const updateMediaForDietPlan = async (req, res, next) => {
    try {
        const {id } = req.body;
        if (req.file) {
          const thumbnailUrl = await fileUploadService.s3Upload([req.file], 'thumbnails');
          req.body.thumbnail = thumbnailUrl ? thumbnailUrl[0].url : req.body.thumbnail;
        }
      const dietPlan = await dietPlanService.updateMediaForDietPlan(id, req.body.thumbnail);
      res.json({ status:true, data: dietPlan, message: 'Diet plan updated successfully' });
    } catch (error) {
      next(error);
    }
  };

  const listDietPlans = async (req, res, next) => {
    try {
      const {sortOrder, keyword, timeFrame, page, limit} = req.query;
      const recipes = await dietPlanService.listDietPlans(sortOrder, keyword, timeFrame, page, limit);
      res.status(httpStatus.OK).json({ status:true, data: recipes });
    } catch (error) {
      next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing recipes', error));
    }
  };
  
  const deleteDietPlan = async (req, res, next) => {
    try {
      await dietPlanService.deleteDietPlan(req.params.id);
      res.status(httpStatus.OK).json({  status:true, message: 'Recipe deleted successfully' });
    } catch (error) {
      next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error deleting recipe', error));
    }
  };


  const addIngredientsToRecipe = async (req, res, next) => {
    try {
      const { recipeId } = req.body;
      const { ingredients } = req.body;
  
      const result = await dietPlanService.addIngredientsToRecipe(recipeId, ingredients);
      res.status(200).json({  status:true, message: 'Ingredients added to the recipe successfully', result });
    } catch (error) {
      next(error);
    }
  };
  
  const updateIngredientInRecipe = async (req, res, next) => {
    try {
      const { recipeId } = req.body;
      const { ingredients } = req.body;
  
      const result = await dietPlanService.updateIngredientsInRecipe(recipeId, ingredients);
      res.status(200).json({  status:true, message: 'Ingredients updated in the recipe successfully', result });
    } catch (error) {
      next(error);
    }
  };
  
  const deleteIngredientFromRecipe = async (req, res, next) => {
    try {
      const { recipeId, ingredientName } = req.query;
      const result = await dietPlanService.deleteIngredientFromRecipe(recipeId, ingredientName);
      res.status(200).json({ status:true,  message: 'Ingredient deleted from the recipe successfully', result });
    } catch (error) {
      next(error);
    }
  };
  
  const updateNutritionalValues = async (req, res, next) => {
    try {
      const { recipeId } = req.body;
      const { nutritionalValues } = req.body;
      const result = await dietPlanService.updateNutritionalValues(recipeId, nutritionalValues);
      res.status(200).json({ status:true, message: 'Nutritional values updated for the recipe successfully', result });
    } catch (error) {
      next(error);
    }
  };

  const reshuffleRecipes = async (req, res, next) => {
    try {
      const { dietPlanId, day, mealType, recipeId, newPosition } = req.body;
      const updatedPlan = await dietPlanService.reshuffleRecipes(dietPlanId, day, mealType, recipeId, newPosition);
      res.status(200).json({  status:true,message: 'Recipes reshuffled successfully', updatedPlan });
    } catch (error) {
      next(error);
    }
  };
  
  const addRecipeToMeal = async (req, res, next) => {
    try {
      const { dietPlanId, day, mealType, recipeIds } = req.body;
      const updatedPlan = await dietPlanService.addRecipesToMeal(dietPlanId, day, mealType, recipeIds);
      res.status(200).json({  status:true,message: 'Recipe added to the meal successfully', updatedPlan });
    } catch (error) {
      next(error);
    }
  };
  
  const removeRecipeFromMeal = async (req, res, next) => {
    try {
      const { dietPlanId, day, mealType, recipeId } = req.body;
      const updatedPlan = await dietPlanService.removeRecipeFromMeal(dietPlanId, day, mealType, recipeId);
      res.status(200).json({  status:true,message: 'Recipe removed from the meal successfully', updatedPlan });
    } catch (error) {
      next(error);
    }
  };

  const viewDietPlan = async (req, res, next) => {
    try {
      const { dietPlanId } = req.params;
      const dietPlan = await dietPlanService.viewDietPlan(dietPlanId);
      res.status(200).json({  status:true, dietPlan });
    } catch (error) {
      next(error);
    }
  };
  
  const viewIngredient = async (req, res, next) => {
    try {
      const { ingredientId } = req.params;
      const ingredient = await dietPlanService.viewIngredient(ingredientId);
      res.status(200).json({  status:true, ingredient });
    } catch (error) {
      next(error);
    }
  };

  const viewRecipe = async (req, res, next) => {
    try {
      const { recipeId } = req.params;
      const updatedPlan = await dietPlanService.viewRecipe(recipeId);
      res.status(200).json({  status:true,updatedPlan });
    } catch (error) {
      next(error);
    }
  };
  

module.exports = {
    addMeasurementUnit,
    updateMeasurementUnit,
    listMeasurementUnit,
    addIngredient,
    updateIngredient,
    listIngredients,
    deleteIngredient,
    addRecipe,
    updateRecipe,
    listRecipes,
    deleteRecipe,
    addDietPlan,
    updateDietPlan,
    listDietPlans,
    deleteDietPlan,
    addIngredientsToRecipe,
    updateIngredientInRecipe,
    deleteIngredientFromRecipe,
    updateNutritionalValues,
    updateMediaForDietPlan,
    updateMediaForRecipe,
    reshuffleRecipes,
    addRecipeToMeal,
    removeRecipeFromMeal,
    viewDietPlan,
    viewRecipe,
    deleteMeasurementUnit,
    viewIngredient
}
