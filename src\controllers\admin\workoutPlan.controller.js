const catchAsync = require("../../utils/catchAsync");
const workoutPlanService = require("../../services/workoutPlan.service");
const ApiError = require("../../utils/ApiError");
const httpStatus = require("http-status");

const getWorkoutPlans = catchAsync(async (req, res, next) => {
    try {
        const { sortOrder, keyword, timeFrame,limit,page } = req.query;
        const workoutPlans = await workoutPlanService.getWorkoutPlans(sortOrder, keyword, timeFrame,{limit,page,sortOrder,sortBy:"createdAt"});
        res.status(200).send({ data: workoutPlans, message: '' });
    } catch (error) {
        next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing workoutPlans', error));
    }
});

const getWorkoutPlan = catchAsync(async (req, res, next) => {
    const workoutPlan = await workoutPlanService.getWorkoutPlanById(req.params.id);
    res.status(200).send({ data: workoutPlan, message: '' });
});

const store = catchAsync(async (req, res, next) => {

    const workoutPlan = await workoutPlanService.createWorkoutPlan(req.body, req.file);
    res.status(201).send({ data: workoutPlan, message: 'WorkoutPlan is created Successfully' });

});

const updateWorkoutPlan = catchAsync(async (req, res, next) => {

    const workoutPlan = await workoutPlanService.updateWorkoutPlanById(req.body._id, req.body, req.file);
    res.status(200).send({ data: workoutPlan, message: 'WorkoutPlan is updated Successfully' });

});

const deleteWorkoutPlan = catchAsync(async (req, res, next) => {

    const workoutPlan = await workoutPlanService.deleteWorkoutPlanById(req.params.id);
    res.status(200).send({ data: workoutPlan, message: 'WorkoutPlan is deleted Successfully' });

});

module.exports = {
    store,
    getWorkoutPlan,
    getWorkoutPlans,
    updateWorkoutPlan,
    deleteWorkoutPlan
}