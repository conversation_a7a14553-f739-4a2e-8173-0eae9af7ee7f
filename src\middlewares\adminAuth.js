const { Admin } = require("../models/admin.model");
const { promisify } = require("util");
const catchAsync = require("../utils/catchAsync");
const jwt = require('jsonwebtoken');

const adminProtect = catchAsync(async (req,res,next) =>{
    // 1) Getting token and check of it's there
    let token;
    if(req.headers.authorization && req.headers.authorization.startsWith('Bearer')){
        token = req.headers.authorization.split(' ')[1];
    }
    if(!token){
        return res.status(401).json({
            status:false,
            msg:'You are not logged in! Please log in to get access.'
        })
    }
    // 2) Token verification 
    const decoded = await promisify(jwt.verify)(token,process.env.JWT_SECRET);

    // 3) Check if user still exists
    const admin = await Admin.findById(decoded.id);
    if(!admin){
        return res.status(401).json({
            status:false,
            msg:'The admin belonging to this token does no longer exists'
        })
    }
    req.admin = admin;
    next();
});

module.exports = {
    adminProtect
}