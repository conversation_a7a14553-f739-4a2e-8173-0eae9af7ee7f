const { User, Client } = require('../models');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { Term } = require('../models/term.model');


async function getTerms() {
    return await Term.find();
}
async function getTermById(id) {
    return await Term.findById(id);
}

async function createTerm(details) {
    return await Term.create(details);
}

async function updateTermById(id, newDetails) {
    const term = await Term.findById(id);
    let updates;

    if (!term) {
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'No terms found');
    }

    updates = { ...newDetails };
    return await Term.findByIdAndUpdate(id, updates, { new: true });
}


module.exports = {
    getTerms,
    updateTermById,
    createTerm,
    getTermById
};