const httpStatus = require("http-status");
const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");
const { Exercise } = require("../models/exercise.model");
const {
  aiWorkoutPlanService,
  aidietPlanService,
  casualAiConvoService,
  aiSuggestionService,
  durationService,
  vectorStoreService,
} = require("../aiServices");

const { Recipe, User } = require("../models");
const UserChatHistory = require("../models/userChatHistory.model");
const { CustomWorkoutPlan } = require("../models/customWorkoutModel");

const openai = require("../config/openAi");
const { CustomDietPlan } = require("../models/customDietPlanModel");

const getAiWorkoutPlan = catchAsync(async (req, res) => {
  const {
    dob,
    name,
    height,
    gender,
    weight,
    activityLevel,
    dietPreference,
    fitnessGoals,
    trainingDays,
  } = req.user;
  const exercisesFromDB = await Exercise.find().lean();

  // console.log(exercisesFromDB[5], "<<<<<<<<<<<<");
  const workoutPlan = await aiWorkoutPlanService.generateWorkoutPlan(
    {
      dob,
      name,
      height,
      gender,
      weight,
      activityLevel,
      dietPreference,
      fitnessGoals,
      trainingDays,
    },
    exercisesFromDB,
    req.body?.duration
  );
  // if (chatHistory) {
  //   console.log("this triggered");
  //   // Update existing chat history
  //   chatHistory.chat.push({ query, botResponse: reply });
  // } else {
  //   // Create new chat history
  //   console.log("this triggered 1");

  //   chatHistory = new UserChatHistory({
  //     user: userData._id,
  //     chat: [{ query, botResponse: reply }],
  //   });
  // }

  // // Save chat history
  // await chatHistory.save();
  workoutPlan.plan = await enrichWorkoutPlanWithMedia(workoutPlan.plan);
  res.status(httpStatus.OK).json({ success: true, workoutPlan });
});

const enrichWorkoutPlanWithMedia = async (plan) => {
  const exerciseIdSet = new Set();

  // 1️⃣ Collect all unique exercise _id values from the plan
  for (const day of plan) {
    for (const section of day.sections) {
      for (const exercise of section.exercises) {
        if (exercise._id) {
          exerciseIdSet.add(exercise._id.toString());
        }
      }
    }
  }
  console.log(exerciseIdSet, "triggered <<<<<<<<<<<<");
  // 2️⃣ Single DB query to fetch thumbnails and videos
  const exerciseIds = Array.from(exerciseIdSet);
  const mediaRecords = await Exercise.find({ _id: { $in: exerciseIds } })
    .select("_id thumbnail video")
    .lean();

  console.log(mediaRecords, " this is nice <<<<<<<<<<<<");
  // 3️⃣ Create a map for fast lookup
  const mediaMap = {};
  for (const exercise of mediaRecords) {
    mediaMap[exercise._id.toString()] = {
      thumbnail: exercise.thumbnail || null,
      video: exercise.video || null,
    };
  }

  // 4️⃣ Inject thumbnail and video into the plan
  for (const day of plan) {
    for (const section of day.sections) {
      for (const exercise of section.exercises) {
        const media = mediaMap[exercise._id];
        exercise.thumbnail = media?.thumbnail ?? null;
        exercise.video = media?.video ?? null;
      }
    }
  }

  return plan;
};

const getAiDietPlan = catchAsync(async (req, res) => {
  const {
    _id,
    dob,
    name,
    height,
    gender,
    weight,
    activityLevel,
    dietPreference,
    fitnessGoals,
    trainingDays,
  } = req.user;

  const mealsFromDB = await Recipe.find();
  let chatHistory = await UserChatHistory.findOne({ user: req.user._id });
  const dietPlan = await aidietPlanService.generateDietPlan(
    {
      _id,
      dob,
      name,
      height,
      gender,
      weight,
      activityLevel,
      dietPreference,
      fitnessGoals,
      trainingDays,
    },
    mealsFromDB,
    req.body?.duration,
    req.body?.prompt,
    chatHistory
  );

  res.status(httpStatus.OK).json({ success: true, dietPlan });
});

const casualReply = catchAsync(async (req, res) => {
  const { query } = req.body;
  const userData = req.user;

  const userWorkoutPlan = await CustomWorkoutPlan.findOne({
    user: req.user._id,
  }).populate("workoutPlan.exercises");

  // console.log(">>>>>>>>>>>>>>", userWorkoutPlan, "<<<<<<<<<<<");

  const reply = await casualAiConvoService.processCasualQuery(
    query,
    userData,
    chatHistory,
    userWorkoutPlan.workoutPlan
  );

  // Check if chat history exists
  console.log(">>>>>>>>>>>>>>>>", reply, "<<<<<<<<<<<<<<<<");
  if (chatHistory) {
    console.log("this triggered");
    // Update existing chat history
    chatHistory.chat.push({ query, botResponse: reply });
  } else {
    // Create new chat history
    console.log("this triggered 1");

    chatHistory = new UserChatHistory({
      user: userData._id,
      chat: [{ query, botResponse: reply }],
    });
  }

  // Save chat history
  await chatHistory.save();

  res.status(httpStatus.OK).json({ success: true, reply });
});

const suggestionReply = catchAsync(async (req, res) => {
  const { query } = req.body;
  // console.log("Started --- given query : ", req.body.query);
  const reply = await aiSuggestionService.processQuery(query);
  // console.log(reply, "from the other ");
  res.status(httpStatus.OK).json({ success: true, reply });
});

const getDuration = catchAsync(async (req, res) => {
  const { query } = req.body;
  const reply = await durationService.getDurationFromQuery(query);
  res.status(httpStatus.OK).json({ success: true, reply });
});

//will be used for chatbot

// const chat = catchAsync(async (req, res) => {
//   const query = req.body.query;
//   const userData = req.user;
//   const userId = req.user._id;

//   let chatHistory = await UserChatHistory.findOne({ user: userId });
//   console.log(chatHistory);

//   let isFollowUp = false;
//   let rephrasedQuery = query;

//   if (chatHistory) {
//     const processedConversation = await processConversation(chatHistory, query); // checks if the query is followup if follow up rephrases it
//     isFollowUp = processedConversation.followup !== 0;
//     rephrasedQuery = isFollowUp ? processedConversation.rephrase : query;
//   }

//   // if (isFollowUp) {
//   //   const casualResponse = await casualAiConvoService.processCasualQuery(
//   //     rephrasedQuery,
//   //     userData,
//   //     chatHistory?.chat || []
//   //   );

//   //   await updateChatHistory(
//   //     userId,
//   //     rephrasedQuery,
//   //     casualResponse,
//   //     chatHistory
//   //   );

//   //   return res.status(200).json({
//   //     type: "casual_response",
//   //     msg: casualResponse,
//   //     data: { value: 0, meal: 0, workout: 0, workoutPlan: 0, dietPlan: 0 },
//   //   });
//   // }

//   let queryWithContext = rephrasedQuery;

//   // if (chatHistory) {
//   //   queryWithContext = await rephraseAsStandalone(chatHistory, rephrasedQuery);
//   // }

//   const suggestionReply = await aiSuggestionService.processQuery(
//     queryWithContext
//   );

//   if (suggestionReply && suggestionReply.value === 1) {
//     return res.status(200).json({
//       type: "suggestion_response",
//       msg: "",
//       data: suggestionReply,
//       rephrasedQuery,
//     });
//   }

//   const casualResponse = await casualAiConvoService.processCasualQuery(
//     rephrasedQuery,
//     userData,
//     chatHistory?.chat || []
//   );

//   // await updateChatHistory(userId, rephrasedQuery, casualResponse, chatHistory);

//   return res.status(200).json({
//     type: "casual_response",
//     msg: casualResponse,
//     data: { value: 0, meal: 0, workout: 0, workoutPlan: 0, dietplan: 0 },
//   });
// });

const chat = catchAsync(async (req, res) => {
  const query = req.body.query;
  const userData = req.user;
  console.log(userData);
  let rephrasedQuery = query;

  // Process query using AI service
  const suggestionReply = await aiSuggestionService.processQuery(
    rephrasedQuery
  );

  if (suggestionReply.meal === 1 || suggestionReply.workout === 1) {
    let message;
    if (suggestionReply.meal === 1 && suggestionReply.workout === 1) {
      message = "Creating a meal and workout plan based on your query...";
    } else if (suggestionReply.meal === 1) {
      message = "Creating a personalized meal plan for you...";
    } else if (suggestionReply.workout === 1) {
      message = "Creating a customized workout plan for you...";
    }

    return res.status(200).json({
      type: "new_plan_response",
      msg: message,
      data: suggestionReply,
      rephrasedQuery,
    });
  }

  const [workoutFileId, dietPlanFileId] = await Promise.all([
    CustomWorkoutPlan.findOne({ user: userData._id }).select("vectorFileId"),
    CustomDietPlan.findOne({ user: userData._id }).select("vectorFileId"),
  ]);

  // 🔹 CASE 2: Existing Plan Response (Call Vector Store Service)
  const vectorResponse = await vectorStoreService.processQuery(
    userData,
    rephrasedQuery,
    workoutFileId ? workoutFileId.vectorFileId : "not-avaliable",
    dietPlanFileId ? dietPlanFileId.vectorFileId : "not-avaliable"
  );

  return res.status(200).json({
    type: "existing_plan_response",
    msg: vectorResponse,
    data: suggestionReply,
    rephrasedQuery,
  });
});

const handleQuery = catchAsync(async (req, res) => {
  const { query, suggestion } = req.body;
  const userData = req.user;

  let rephrasedQuery = query;
  const response = await processNewPlan(rephrasedQuery, userData, suggestion);
  return res.status(httpStatus.OK).json({
    success: true,
    data: response,
  });
});

const processNewPlan = async (query, userData, suggestion) => {
  // Get durations from query or use defaults

  const {
    workout_duration,
    meal_duration,
  } = await durationService.getDurationFromQuery(query);
  const defaultWorkoutDuration = workout_duration || 30;
  const defaultMealDuration = meal_duration || 30;

  const getWorkoutData = async () => {
    try {
      const pipeline = [
        // Stage 1: Join equipment details
        {
          $lookup: {
            from: "equipment",
            localField: "equipment",
            foreignField: "_id",
            as: "equipmentDetails",
          },
        },

        // Stage 2: Filter by user preferences
        {
          $match: {
            $or: [
              // Filter by equipment if user has equipment
              ...(userData.equipmentsAvaliable > 0
                ? [
                    {
                      equipment: { $in: userData.equipmentsAvaliable },
                    },
                  ]
                : []),

              // Filter by target body parts if user has preferences
              ...(userData.targetBodyPart && userData.targetBodyPart.length > 0
                ? [
                    {
                      musclegroupsinvolved: { $in: userData.targetBodyPart },
                    },
                  ]
                : []),
            ].filter((condition) => Object.keys(condition).length > 0),
          },
        },

        // Stage 3: Project desired fields
        {
          $project: {
            name: 1,
            description: 1,
            exerciseTypes: 1,
            level: 1,
            musclegroupsinvolved: 1,
            equipment: "$equipmentDetails.name",
          },
        },
      ];

      let matchedWorkouts = await Exercise.aggregate(pipeline);

      // If no matches found with user equipment, return all exercises
      if (matchedWorkouts.length === 0 && userEquipmentIds.length > 0) {
        const fallbackPipeline = [
          {
            $lookup: {
              from: "equipment",
              localField: "equipment",
              foreignField: "_id",
              as: "equipmentDetails",
            },
          },
          {
            $project: {
              name: 1,
              description: 1,
              exerciseTypes: 1,
              level: 1,
              musclegroupsinvolved: 1,
              equipment: "$equipmentDetails.name",
            },
          },
        ];

        matchedWorkouts = await Exercise.aggregate(fallbackPipeline);
      }

      return matchedWorkouts;
    } catch (error) {
      console.error("Error fetching workout data:", error);
      throw new Error("Failed to fetch workout data");
    }
  };

  const getMealData = async () => {
    let meals = await Recipe.find({
      planTypes: { $in: userData.userMealTypes },
      "ingredients.name": {
        $nin: [...userData.allergies, ...userData.dietaryRestrictions],
      },
    }).lean();

    // If no recipes match the filters, fetch all recipes
    if (!meals || meals.length === 0) {
      console.log("No matching recipes found. Fetching all recipes...");
      return Recipe.find().lean();
    }

    return meals;
  };

  // 🔹 CASE 1: Combined Workout and Meal Plan
  if (suggestion.workout === 1 && suggestion.meal === 1) {
    const [exercisesFromDB, mealsFromDB] = await Promise.all([
      getWorkoutData(),
      getMealData(),
    ]);

    const [workoutPlan, dietPlan] = await Promise.all([
      aiWorkoutPlanService.generateWorkoutPlan(
        userData,
        exercisesFromDB,
        defaultWorkoutDuration,
        query
      ),
      aidietPlanService.generateDietPlan(
        userData,
        mealsFromDB,
        defaultMealDuration,
        query
      ),
    ]);

    const populatedDietPlan = await fetchRecipeDetails(dietPlan.dietPlan);
    workoutPlan.plan = await enrichWorkoutPlanWithMedia(workoutPlan.plan);

    return {
      type: "combined_plan",
      workoutPlan,
      dietPlan: {
        description: dietPlan.description,
        plan: populatedDietPlan,
      },
    };
  }

  // 🔹 CASE 2: Workout Plan Only
  if (suggestion.workout === 1) {
    const exercisesFromDB = await getWorkoutData();
    const workoutPlan = await aiWorkoutPlanService.generateWorkoutPlan(
      userData,
      exercisesFromDB,
      defaultWorkoutDuration,
      query
    );
    workoutPlan.plan = await enrichWorkoutPlanWithMedia(workoutPlan.plan);
    return {
      type: "workout_plan",
      workoutPlan,
    };
  }

  // 🔹 CASE 3: Meal Plan Only
  if (suggestion.meal === 1) {
    const mealsFromDB = await getMealData();
    const dietPlan = await aidietPlanService.generateDietPlan(
      userData,
      mealsFromDB,
      defaultMealDuration,
      query
    );

    // console.log("@@@@@@@@@@", dietPlan.dietPlan, "<<<<<<<<<");

    const populatedDietPlan = await fetchRecipeDetails(dietPlan.dietPlan);

    return {
      type: "diet_plan",
      dietPlan: {
        description: dietPlan.description,
        plan: populatedDietPlan,
      },
    };
  }

  // 🔹 CASE 4: No Matching Suggestion
  return {
    type: "no_plan",
    message: "No matching plan found for the query.",
  };
};

const chatHistory = catchAsync(async (req, res) => {
  console.log(req.user);
  const userId = req.user._id;
  const user = await User.findById(userId).select("+threadId");
  if (!user.threadId) {
    return res.status(200).json({
      success: true,
      msg: "chat history doesnt exist",
      data: response,
    });
  }
  const response = await vectorStoreService.getChatHistoryFromThread(
    user.threadId
  );
  return res.status(200).json({
    success: true,
    msg: "chat history fetched",
    data: await formatChatHistoryForFrontend(response),
  });
});

///////// vector store utils /////////////
const getFiles = catchAsync(async (req, res) => {
  const files = await listUploadedFiles();

  res.status(httpStatus.OK).json({ data: files });
});

const getAllVectorStore = catchAsync(async (req, res) => {
  try {
    const vectorStores = await getAllVectorStores();
    res.status(200).json(vectorStores);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Failed to fetch vector stores", error: error.message });
  }
});

const getAllVectorStores = async () => {
  try {
    const response = await openai.beta.vectorStores.list();

    return { data: response.body.data };
  } catch (error) {
    console.error("❌ Error deleting vector stores:", error);
    throw error;
  }
};

const deleteAllVectorStore = catchAsync(async (req, res) => {
  try {
    const vectorStores = await deleteAllVectorStores();
    res.status(200).json(vectorStores);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Failed to fetch vector stores", error: error.message });
  }
});

const deleteAllVectorStores = async () => {
  try {
    const response = await openai.beta.vectorStores.list();

    if (!response.data || response.data.length === 0) {
      console.log("✅ No vector stores to delete.");
      return { message: "No vector stores found." };
    }

    console.log(`🔄 Deleting ${response.data.length} vector stores...`);

    // Filter out the store you don't want to delete
    const storesToDelete = response.data.filter(
      (store) => store.id !== "vs_67aedba477088191b2635efda23a7725"
    );

    // Delete all stores concurrently
    await Promise.all(
      storesToDelete.map(async (store) => {
        await openai.beta.vectorStores.del(store.id);
        console.log(`🗑️ Deleted vector store: ${store.id}`);
      })
    );

    console.log("✅ All eligible vector stores deleted.");

    return { message: "Vector stores deleted successfully." };
  } catch (error) {
    console.error("❌ Error deleting vector stores:", error);
    throw error;
  }
};

const deleteAllFile = catchAsync(async (req, res) => {
  const files = await deleteAllFiles();

  res.status(200).json({
    message:
      files.length > 0 ? "File deletion completed" : "No files found to delete",
    files,
  });
});

const listUploadedFiles = async () => {
  try {
    const response = await openai.files.list();

    const files = response.data;

    return files;
  } catch (error) {
    console.error(`Error listing files: ${error}`);
    return [];
  }
};

const deleteAllFiles = async () => {
  try {
    const files = await listUploadedFiles();

    if (!files || files.length === 0) {
      console.warn("⚠️ No files found to delete.");
      return [];
    }

    // Filter out files you want to keep
    const filesToDelete = files.filter(
      (file) =>
        file.id !== "file-JSZ384cjDG4JS7bSpwYf71" &&
        file.id !== "file-4vPJNwA5BiX5jvzDQkVvkW"
    );

    if (filesToDelete.length === 0) {
      console.log("✅ No eligible files to delete.");
      return [];
    }

    // Delete files concurrently
    await Promise.all(
      filesToDelete.map(async (file) => {
        await deleteFile(file.id);
        console.log(`🗑️ Deleted file: ${file.id}`);
      })
    );

    console.log("✅ All eligible files deleted successfully.");
  } catch (error) {
    console.error("❌ Error deleting files:", error);
    throw new Error("File deletion failed. See logs for details.");
  }
};

const deleteFile = async (fileId) => {
  try {
    await openai.files.del(fileId);
    console.log(`🗑️ Successfully deleted file: ${fileId}`);
  } catch (error) {
    console.error(`❌ Failed to delete file ${fileId}:`, error);
    throw error;
  }
};
///////// vector store utils /////////////\

/// general utilsss ///
async function formatChatHistoryForFrontend(messages) {
  return messages.map(({ created_at, role, content }) => ({
    sender: role,
    text: content.map((c) => c.text?.value || "").join(" "),
    timestamp: new Date(created_at * 1000).toISOString(),
  }));
}

async function fetchRecipeDetails(dietPlan) {
  if (!dietPlan || dietPlan.length === 0) return [];

  // Step 1: Collect unique recipe IDs
  const recipeIds = new Set();
  dietPlan.forEach((day) => {
    if (day.breakfast?._id) recipeIds.add(day.breakfast._id.toString());
    if (day.lunch?._id) recipeIds.add(day.lunch._id.toString());
    if (day.dinner?._id) recipeIds.add(day.dinner._id.toString());
  });

  // Step 2: Fetch all recipe documents
  const recipes = await Recipe.find({
    _id: { $in: Array.from(recipeIds) },
  }).lean();

  // Step 3: Map recipes by ID
  const recipeMap = {};
  recipes.forEach((recipe) => {
    recipeMap[recipe._id.toString()] = recipe;
  });

  // Step 4: Attach populated meals with meal & servingSize, and remove `name`
  const updatedDietPlan = dietPlan.map((day) => {
    const getMeal = (meal) => {
      const recipe = recipeMap[meal?._id?.toString()];
      if (!recipe) return meal;

      const { name, ...rest } = recipe;

      return {
        ...rest,
        _id: recipe._id, // keep _id first
        meal: name, // instead of `name`
        servingSize: meal.servingSize,
      };
    };

    return {
      day: day.day,
      breakfast: getMeal(day.breakfast),
      lunch: getMeal(day.lunch),
      dinner: getMeal(day.dinner),
    };
  });

  return updatedDietPlan;
}

/// general utilsss ///
module.exports = {
  getAiWorkoutPlan,
  getAiDietPlan,
  casualReply,
  suggestionReply,
  getDuration,
  chat,
  handleQuery,
  deleteAllFile,
  deleteAllVectorStore,
  getAllVectorStore,
  getFiles,
  chatHistory,
};
