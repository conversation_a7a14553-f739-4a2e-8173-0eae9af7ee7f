const Agenda = require("agenda");
const { Notification } = require("../models/notification.model");
const { mongoose } = require("./config");
const UserNotification = require("../models/userNotification.model");
const notiFunc = require("../microservices/notification.service");
const { User } = require("../models/user.model");
const { DailyReminder } = require("../models/dailyReminder.model");
const { CustomWorkoutPlan } = require("../models/customWorkoutModel");
const moment = require("moment");
const { CustomDietPlan } = require("../models/customDietPlanModel");
const { scheduleNextMealNotification } = require("../utils/helper");

const agenda = new Agenda({
  db: { address: mongoose.url }, // Replace with your MongoDB connection string
  collection: "agendaJobs", // Specify the name of the collection where jobs will be stored
});

// Define a job for adding a reminder
agenda.define("scheduleNotification", async (job) => {
  const { notiId } = job.attrs.data;

  const notification = await Notification.findById(notiId);

  if (notification.isScheduled) {
    const users = await User.find({
      isBlocked: false,
      isDeleted: { $ne: true },
    }).select("_id");

    const userNotiData = [];

    users.forEach((user) => {
      userNotiData.push({
        receiver: user._id,
        notification: notification._id,
        title: notification.title,
        description: notification.description,
      });
    });

    const userNotifications = await UserNotification.create(userNotiData);

    await notiFunc.sendToTopic("all", {
      title: notification.title,
      body: notification.description,
    });

    notification.isDelivered = true;
    await notification.save();
  }
});

// agenda.define('WIR', async (job) => {
//   const { user } = job.attrs.data;

//   const userData = await User.findById(user);

//   if ( userData && userData.WIR && userData.WIR.isActive ){
//       const userNotiData = [];

//       users.forEach(user => {
//           userNotiData.push({
//               receiver:user._id,
//               notification:reminderDetail._id,
//               title:reminderDetail.title,
//               description:reminderDetail.description,
//           })
//       });

//       const userNotifications = await UserNotification.create(userNotiData);

//       await notiFunc.sendToTopic("WIR", { title: reminderDetail.title, body: reminderDetail.description });

//       reminderDetail.isDelivered = true;
//       await reminderDetail.save();
//   }

// });

agenda.define("MR", async (job) => {
  const { reminder } = job.attrs.data;

  const reminderDetail = await DailyReminder.findById(reminder);

  if (reminderDetail) {
    const users = await User.find({
      isBlocked: false,
      isDeleted: { $ne: true },
      _id: userId,
    }).select("_id");

    const userNotiData = [];

    users.forEach((user) => {
      userNotiData.push({
        receiver: user._id,
        notification: reminderDetail._id,
        title: reminderDetail.title,
        description: reminderDetail.description,
      });
    });

    const userNotifications = await UserNotification.create(userNotiData);

    await notiFunc.sendToTopic("MR", {
      title: reminderDetail.title,
      body: reminderDetail.description,
    });

    reminderDetail.isDelivered = true;
    await reminderDetail.save();
  }
});

agenda.define("sendWorkoutNotification", async (job) => {
  const { userId, workoutDetails } = job.attrs.data;

  console.log(`🚀 Job 'sendWorkoutNotification' started for user: ${userId}`);
  console.log(`[JOB DATA]`, JSON.stringify(job.attrs.data, null, 2));

  const user = await User.findOne({
    _id: userId,
    isBlocked: false,
    isDeleted: { $ne: true },
  });

  if (!user) {
    console.warn(`❌ User not found or inactive: ${userId}`);
    return;
  }

  try {
    const workoutPlan = await CustomWorkoutPlan.findOne({ user: userId });

    if (!workoutPlan) {
      console.error(`❌ No workout plan found for user ${userId}`);
      return;
    }

    if (!workoutPlan.notificationSchedule?.enabled) {
      console.log(`🔕 Notifications disabled for user ${userId}`);
      return;
    }

    const todayDate = moment().format("YYYY-MM-DD");
    const todayWorkout = workoutPlan.workoutPlan.find(
      (day) => day.date === todayDate
    );

    if (!todayWorkout) {
      console.warn(`⚠️ No workout found for today (${todayDate})`);
      return;
    }

    if (!Array.isArray(todayWorkout.sections)) {
      console.error(`❌ Invalid or missing sections in today's workout.`);
      return;
    }

    console.log(`📋 Found today's workout entry:`, {
      date: todayWorkout.date,
      sections: todayWorkout.sections.length,
    });

    // ✅ Format message
    const sectionMessages = todayWorkout.sections.map((section, sIdx) => {
      if (!Array.isArray(section.exercises)) {
        console.warn(
          `[WARN] Section [${sIdx}] "${section.section}" has no exercises.`
        );
        return `➡️ ${section.section}\n(No exercises listed)`;
      }

      const sectionTitle = `➡️ ${section.section}`;
      const exercisesList = section.exercises
        .map((exercise, idx) => {
          console.log(
            `[DEBUG] Section: ${section.section} | Exercise ${idx + 1}:`,
            exercise.name
          );
          return `${idx + 1}. ${exercise.name} (${exercise.equipment?.join(
            ", "
          ) || "No Equipment"}) - Weight: ${exercise.weight ||
            "N/A"}, Sets: ${exercise.sets ?? "-"}, Reps: ${exercise.reps ??
            "-"}`;
        })
        .join("\n");

      return `${sectionTitle}\n${exercisesList}`;
    });

    const fullMessage = `🏋️ Workout Reminder for ${todayDate}\nStarts at: ${
      workoutPlan.notificationSchedule.time
    }\n\n${sectionMessages.join("\n\n")}`;

    // 🔔 Send Notification
    console.log(`📨 Sending notification to user ${userId}...`);
    await notiFunc.sendToTopic(userId.toString(), {
      title: "Workout Reminder",
      body: fullMessage,
    });

    console.log(`✅ Notification sent successfully to user ${userId}`);

    // 🔁 Schedule next workout notification
    const todayIndex = workoutPlan.workoutPlan.findIndex(
      (day) => day.date === todayDate
    );
    const nextWorkout = workoutPlan.workoutPlan[todayIndex + 1];

    if (nextWorkout) {
      const [hour, minute] = workoutPlan.notificationSchedule.time
        .split(":")
        .map(Number);

      const nextNotificationTime = moment(nextWorkout.date)
        .set({ hour, minute, second: 0 })
        .subtract(15, "minutes");

      console.log(
        `📅 Scheduling next workout notification for ${
          nextWorkout.date
        } at ${nextNotificationTime.format()}`
      );
      console.log(nextWorkout, "<<<<<<<<<<<<<<");
      await agenda.schedule(
        nextNotificationTime.toDate(),
        "sendWorkoutNotification",
        {
          userId,
          workoutDetails: nextWorkout,
        }
      );
    } else {
      console.log(
        `🏁 All workouts completed for user ${userId}. No future notifications.`
      );
    }
  } catch (error) {
    console.error(
      `❌ Error during workout notification for user ${userId}:`,
      error
    );
  }
});

agenda.define("send-diet-notification", async (job) => {
  const {
    userId,
    mealDetails,
    date,
    dietPlan,
    meal,
    notificationSchedule,
  } = job.attrs.data;

  const user = await User.find({
    isBlocked: false,
    isDeleted: { $ne: true },
    _id: userId,
  }).select("_id");

  if (!user) {
    console.log(`❌ user is blocked or deleted ${userId}`);
    return;
  }

  if (!userId || !dietPlan || !notificationSchedule || !mealDetails || !meal) {
    console.error("❌ Job failed: Missing required data");
    return;
  }

  if (!notificationSchedule.enabled) {
    console.log(`❌ Notifications are disabled for user ${userId}`);
    return;
  }

  await notiFunc.sendToTopic(userId.toString(), {
    title: `Time for your meal: ${meal}`,
    body: mealDetails.thumbnail,
  });

  console.log(`Sent notification for ${meal} on ${date} to user ${userId}`);

  // Schedule the next meal notification dynamically
  await scheduleNextMealNotification(userId, dietPlan, notificationSchedule);
});

// Start Agenda

(async () => await agenda.start())();

agenda.on("start", (job) => console.log(`🚀 Job '${job.attrs.name}' started`));
agenda.on("complete", (job) =>
  console.log(`✅ Job '${job.attrs.name}' completed`)
);
agenda.on("ready", () => console.log("✅ Agenda connected successfully!"));

agenda.on("fail", (err, job) => {
  console.error(`Job failed with error: ${err.message}`);
});

module.exports = agenda;
