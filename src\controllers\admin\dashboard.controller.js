const catchAsync = require("../../utils/catchAsync");
const  {Payment}  = require("../../models/payment.model");
const { User } = require("../../models");
const { LoginLog } = require("../../models/loginLog.model");
const { paymentService } = require("../../services");

const revenueGraph = catchAsync( async(req,res,next) => {

    const {year,duration,type} = req.query
    const filter = {};

    let currentYear = new Date().getFullYear();
    if (year){   
        let yearInt = parseInt(req.query.year);
        currentYear =  yearInt;//new Date().setFullYear(yearInt);
    }

    let payment;
    if(duration == "year"){
        payment = await Payment.getSalesAndRevenueByMonth(filter,currentYear);
    }else if(duration == "week"){
        payment = await Payment.getSalesAndRevenueByWeekDays(filter,type);
    }else if(duration == "month"){
        payment = await Payment.getSalesAndRevenueByDayofMonth(filter,currentYear);
    }

    return res.status(200).json({
        status:true,
        data:payment
    })

});

const engagementGraph = catchAsync( async(req,res,next) => {

    // const {year,duration,type} = req.query
    const filter = {};

  
    const thisWeek = await LoginLog.getGraphDataByWeekDays(filter,"this");
    const lastWeek = await LoginLog.getGraphDataByWeekDays(filter,"last");
  

    return res.status(200).json({
        status:true,
        data:{thisWeek,lastWeek}
    })

});

const stats = catchAsync( async(req,res,next) => {

  const [totalUsers,totalRevenue ] = await Promise.all([
    User.find({isDeleted:{$ne:true}}).count(),
    paymentService.getRevenue({})
  ]);

  return res.status(200).json({
      status:true,
      data:{
        totalUsers,
        totalSubscription:0,
        newSubscription:0,
        totalRevenue
      }
  })
    
});


const userStats = catchAsync( async(req,res,next) => {


    const allUsers = await User.aggregate([
        {
          $group: {
            _id: '$gender',
            count: { $sum: 1 }
          }
        }
    ]);


    const users = await User.aggregate([
        {
          $addFields: {
            age: {
              $floor: {
                $divide: [
                  {
                    $subtract: [new Date(), '$dob']
                  },
                  1000 * 60 * 60 * 24 * 365
                ]
              }
            }
          }
        },
        {
          $facet: {
            "0-18": [
              { $match: { age: { $lte: 18 } } },
              {
                $group: {
                  _id: "$gender",
                  count: { $sum: 1 }
                }
              }
            ],
            "18-30": [
              { $match: { age: { $gt: 18, $lte: 30 } } },
              {
                $group: {
                  _id: "$gender",
                  count: { $sum: 1 }
                }
              }
            ],
            "30-45": [
              { $match: { age: { $gt: 30, $lte: 45 } } },
              {
                $group: {
                  _id: "$gender",
                  count: { $sum: 1 }
                }
              }
            ],
            "45-60": [
              { $match: { age: { $gt: 45, $lte: 60 } } },
              {
                $group: {
                  _id: "$gender",
                  count: { $sum: 1 }
                }
              }
            ],
            "60+": [
              { $match: { age: { $gt: 60 } } },
              {
                $group: {
                  _id: "$gender",
                  count: { $sum: 1 }
                }
              }
            ]
          }
        }
      ]);

      const data = [

        {
            age: "0-18",
            male: getGenderCount(users[0]["0-18"],"male"),
            female: getGenderCount(users[0]["0-18"],"female"),
            other: getGenderCount(users[0]["0-18"],null)
        },
        {
            age: "18-30",
            male: getGenderCount(users[0]["18-30"],"male"),
            female: getGenderCount(users[0]["18-30"],"female"),
            other: getGenderCount(users[0]["18-30"],null)
        },
        {
            age: "30-45",
            male: getGenderCount(users[0]["30-45"],"male"),
            female: getGenderCount(users[0]["30-45"],"female"),
            other: getGenderCount(users[0]["30-45"],null)
        },
        {
            age: "45-60",
            male: getGenderCount(users[0]["45-60"],"male"),
            female: getGenderCount(users[0]["45-60"],"female"),
            other: getGenderCount(users[0]["45-60"],null)
        },
        {
            age: "60+",
            male: getGenderCount(users[0]["60+"],"male"),
            female: getGenderCount(users[0]["60+"],"female"),
            other: getGenderCount(users[0]["60+"],null)
        },


      ];



    return res.status(200).json({
        status:true,
        data:{all:allUsers, agewise:data}
    })

});



function getGenderCount (arr,gender) {
   const count = arr.find(ele => ele._id == gender);
   return count ? count.count : 0; 
   
}


module.exports = {
    revenueGraph,
    userStats,
    engagementGraph,
    stats
}