const express = require("express");
const router = express.Router();
const { firebaseAuth } = require("../../middlewares/firebaseAuth");
const {
  customWorkoutController,
  customDietplanController,
} = require("../../controllers");

router.post(
  "/workout/create",
  firebaseAuth,
  customWorkoutController.createWorkoutPlan
);

router.get(
  "/workout/",
  firebaseAuth,
  customWorkoutController.getCustomWorkoutPlan
);

// router.post(
//   "/workout/start",
//   firebaseAuth,
//   customWorkoutController.startWorkout
// );
// router.post("/workout/stop", firebaseAuth, customWorkoutController.endWorkout);

router.post(
  "/diet/create",
  firebaseAuth,
  customDietplanController.createDietPlan
);

// router.get(
//   "/progress",
//   firebaseAuth,
//   customWorkoutController.getProgressByDate
// );

router.patch("/diet/", firebaseAuth, customDietplanController.updateDietPlan);
router.get("/diet/", firebaseAuth, customDietplanController.getCustomDietPlan);

// router.get("/diet/", firebaseAuth, customWorkoutController.createWorkoutPlan);
// router.post("/dietplan", firebaseAuth, aiController.getAiDietPlan);

module.exports = router;
