const mongoose = require("mongoose");
const { paginate } = require("./plugins/paginate");

const userWorkoutSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    planType: {
      type: String,
      enum: ["custom", "default"],
      required: true,
    },
    workoutPlanId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "WorkoutPlan",
      default: null, // Only for default plans
    },
    customPlanId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "CustomWorkoutPlan",
      default: null, // Only for custom plans
    },
    startTime: {
      type: Date,
      default: null,
    },
    endTime: {
      type: Date,
      default: null,
    },
    duration: {
      type: Number,
      default: null, // Will be auto-calculated
    },
    calories: {
      type: Number,
      default: null,
    },
    exercisesPerformed: [
      {
        exerciseId: { type: mongoose.Schema.Types.ObjectId, ref: "Exercise" },
        sets: Number,
        reps: String,
        weight: String,
        caloriesBurnt: Number,
      },
    ],
  },
  { timestamps: true }
);

// Auto-calculate `duration` when `endTime` is set
userWorkoutSchema.pre("save", function(next) {
  if (this.startTime && this.endTime) {
    this.duration = Math.round((this.endTime - this.startTime) / (1000 * 60)); // Convert to minutes
  }
  next();
});

userWorkoutSchema.plugin(paginate);

const UserWorkout = mongoose.model("UserWorkout", userWorkoutSchema);
module.exports = { UserWorkout };
