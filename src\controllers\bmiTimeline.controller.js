const catchAsync = require("../utils/catchAsync");
const bmiTimelineService = require("../services/bmiTimeline.service");
const { getPaginateConfig } = require("../utils/queryPHandler");

const getBmiTimelines = catchAsync(async (req, res, next) => {
    const {filters} = getPaginateConfig(req.query);
    filters.userId = req.user._id;
    console.log(filters)
    let start;
    let end;
    let timeFrame = filters.timeFrame;
    if(filters.timeFrame){
    
        const today = new Date();
        if(filters.timeFrame == "thisMonth"){
            start = new Date(today.getFullYear(), today.getMonth(), 1);
            start.setHours(0, 0, 0, 0);
            
            end = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            end.setHours(23, 59, 59, 999);
        }else if(filters.timeFrame == "thisWeek"){
            start = new Date(today.setDate(today.getDate() - today.getDay()));
            start.setHours(0, 0, 0, 0);

            end = new Date(start);
            end.setDate(end.getDate() + 6);
            end.setHours(23, 59, 59, 999);
        }else if(filters.timeFrame == "year"){

            const year = filters.year ? filters.year : today.getFullYear();
            start = new Date(year, 0, 1);
            start.setHours(0, 0, 0, 0);
            
            end = new Date(year , 11, 31);
            end.setHours(23, 59, 59, 999);
        }else if (filters.timeFrame == "lastWeek") {
            // Get the previous Sunday
            let lastSunday = new Date(today);
            lastSunday.setDate(today.getDate() - today.getDay() - 7);
            lastSunday.setHours(0, 0, 0, 0);
            
            start = new Date(lastSunday);
        
            // Get the following Saturday
            end = new Date(start);
            end.setDate(start.getDate() + 6);
            end.setHours(23, 59, 59, 999);
        }else{
            const year = filters.year ? filters.year : today.getFullYear();
            start = new Date(year, 0, 1);
            start.setHours(0, 0, 0, 0);
            
            end = new Date(year , 11, 31);
            end.setHours(23, 59, 59, 999);
        }
        delete filters.timeFrame;

        filters.createdAt = { $gte: start, $lte: end }
    }
    console.log(filters)
    let sortOrder = -1;
    if(!timeFrame && timeFrame == "year"){
        sortOrder = 1;
    }

    const bmiTimelines = (await bmiTimelineService.getBmiTimelines(filters,{createdAt:sortOrder}));
    let data;
    if(timeFrame == "year"){
        data = {
                January: bmiTimelines.filter(e => e.month == 1),
                February: bmiTimelines.filter(e => e.month == 2),
                March: bmiTimelines.filter(e => e.month == 3),
                April: bmiTimelines.filter(e => e.month == 4),
                May: bmiTimelines.filter(e => e.month == 5),
                June: bmiTimelines.filter(e => e.month == 6),
                July: bmiTimelines.filter(e => e.month == 7),
                August: bmiTimelines.filter(e => e.month == 8),
                September: bmiTimelines.filter(e => e.month == 9),
                October: bmiTimelines.filter(e => e.month == 10),
                November: bmiTimelines.filter(e => e.month == 11),
                December: bmiTimelines.filter(e => e.month == 12)
        }
    }else{
        data = bmiTimelines;
    }


    res.status(200).send({ data: data, message: '' });

});

const getBmiTimeline = catchAsync(async (req, res, next) => {

    const bmiTimeline = await bmiTimelineService.getBmiTimelineById(req.params.id);

    res.status(200).send({ data: bmiTimeline, message: '' });

});

const store = catchAsync(async (req, res, next) => {
    const user = req.user;
    const today = new Date();
    const month = today.getMonth() + 1;
    const year = today.getFullYear();
    const bmiTimeline = await bmiTimelineService.createBmiTimeline({ ...req.body, userId: user._id ,month,year}, req.file);
    res.status(201).send({ data: bmiTimeline, message: 'BmiTimeline is created Successfully' });

});

const updateBmiTimeline = catchAsync(async (req, res, next) => {
    const user = req.user;
    const bmiTimeline = await bmiTimelineService.updateBmiTimelineById(req.body._id, { ...req.body, userId: user._id }, req.file);
    res.status(200).send({ data: bmiTimeline, message: 'BmiTimeline is updated Successfully' });

});

const deleteBmiTimeline = catchAsync(async (req, res, next) => {

    const bmiTimeline = await bmiTimelineService.deleteBmiTimelineById(req.params.id);
    res.status(200).send({ data: bmiTimeline, message: 'BmiTimeline is deleted Successfully' });

});

module.exports = {
    store,
    getBmiTimeline,
    getBmiTimelines,
    updateBmiTimeline,
    deleteBmiTimeline
}