const mongoose = require('mongoose');
const { paginate } = require('./plugins/paginate');

const dietCategorySchema = new mongoose.Schema(
    {
        thumbnail: {
            type: {
                key: String,
                url: String,
            },
            default: null,
        },
        name: {
            type: String,
            default: null,
        }
    },
    { timestamps: true }
);

dietCategorySchema.plugin(paginate);

const DietCategory = mongoose.model('DietCategory', dietCategorySchema);
module.exports = {
    DietCategory
};