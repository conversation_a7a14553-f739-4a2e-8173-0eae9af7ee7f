const Joi = require('joi');
const { objectId } = require('./custom.validation');

const exerciseSchema = {
    name: Joi.string()
        .trim()
        .required(),
    description: Joi.string()
        .trim()
        .required()
};


const AddExercise = {
    body: Joi.object().keys({
        ...exerciseSchema,
    }),
};

const EditExercise = {
    body: Joi.object().keys({
        _id: Joi.string()
            .custom(objectId),
        ...exerciseSchema,
    }),
};

module.exports = {
    AddExercise,
    EditExercise
};
