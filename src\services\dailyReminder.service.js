const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { fileUploadService } = require('../microservices');
const { DailyReminder } = require('../models/dailyReminder.model');

async function getDailyReminderById(id) {
    const dailyReminder = await DailyReminder.findById(id);
    return dailyReminder;
}

async function getDailyReminders(filters, options) {
    return await DailyReminder.paginate(filters, options);
}

async function getDailyReminder(filters) {
    return await DailyReminder.find(filters);
}

async function createDailyReminder(details, image) {
    let data = { ...details };

    if (image) {
        const [thumbnail] = await fileUploadService.s3Upload([image], 'thumbnail').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload thumbnail');
        })
        data = { ...data, thumbnail };
    };

    return await DailyReminder.create(data);
}

async function updateDailyReminderById(id, body, image) {
    const dailyReminder = await DailyReminder.findById(id);
    let updates = { ...body };

    if (image) {
        const [thumbnail] = await fileUploadService.s3Upload([image], 'thumbnail').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload thumbnail');
        });
        if (dailyReminder.thumbnail) {
            const oldPicKey = dailyReminder.thumbnail.key;
            await fileUploadService
                .s3Delete(oldPicKey)
                .catch(err => console.log('Failed to delete thumbnail', oldPicKey));
        }
        updates = { ...updates, thumbnail };
    }

    return await DailyReminder.findByIdAndUpdate(id, updates, { new: true });
}

async function deleteDailyReminderById(id) {
    try {
        await DailyReminder.findByIdAndDelete(id);
        return true;
    } catch (err) {
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to delete the DailyReminder');
    }
}

module.exports = {
    getDailyReminderById,
    getDailyReminders,
    getDailyReminder,
    createDailyReminder,
    updateDailyReminderById,
    deleteDailyReminderById
}