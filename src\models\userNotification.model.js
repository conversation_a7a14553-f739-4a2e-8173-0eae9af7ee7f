const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const userNotificationSchema = new Schema({
    receiver:{
        type:mongoose.Schema.Types.ObjectId,
        ref:"User"
    },
    notification:{
        type:mongoose.Schema.Types.ObjectId,
        ref:"Notification"
    },
    title:{
        type:String
    },
    description:{
        type:String
    },
    notificationType:{ 
        type:String,
    },
    isRead:{
        type:Boolean,
        default:false
    },
    thumbnail:{
        type:String,
        default:null
    }
},
{timestamps:true});

module.exports = mongoose.model('userNotification', userNotificationSchema);