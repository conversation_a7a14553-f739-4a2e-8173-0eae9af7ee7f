const express = require('express');
const router = express.Router();

const validate = require('../../../middlewares/validate');
const { adminProtect } = require('../../../middlewares/adminAuth');
const termValidation = require('../../../validations/term.validation');

const termController = require('../../../controllers/admin/term.controller');


router.post(
    '/add',
    adminProtect,
    validate(termValidation.AddTerm),
    termController.store
);

router.post(
    '/update',
    adminProtect,
    validate(termValidation.EditTerm),
    termController.updateTerm
)

router.get(
    '/list',
    adminProtect,
    termController.getTerms
);

router.get(
    '/:id',
    adminProtect,
    termController.getTerm
);
module.exports = router;