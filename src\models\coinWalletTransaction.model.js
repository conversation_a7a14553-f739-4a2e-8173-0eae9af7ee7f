const mongoose = require('mongoose');
const { paginate } = require('./plugins/paginate');

const coinWalletTransactionSchema = new mongoose.Schema(
    {
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "User",
        },
        walletId:{
            type: mongoose.Schema.Types.ObjectId,
            ref: "UserCoinWallet",
        },
        transactionType:{
            type:String,
            enum:["debit","credit"]
        },
        coinOffer:{
            type: mongoose.Schema.Types.ObjectId,
            ref: "CoinOffer",
        },
        coins:{
            type:Number,
            default:0
        },
        balanceBefore:{
            type:Number,
            default:0
        },
        balanceAfter:{
            type:Number,
            default:0
        },
        description:{
            type:String
        },
        challenge:{
            type: mongoose.Schema.Types.ObjectId,
            ref: "challenge",
        },
        paymentId:{
            type: mongoose.Schema.Types.ObjectId,
            ref:"Payment"
        }
    },
    { timestamps: true }
);

coinWalletTransactionSchema.plugin(paginate);

coinWalletTransactionSchema.statics.getSalesAndRevenueByWeekDays = async function(filters, week) {
    const today = new Date();
    let WeekStartDate;
    let WeekEndDate;
    const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    if(week == "last"){
      WeekStartDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay() - 6);
      WeekEndDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay());
    }else{
      WeekStartDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay());
      WeekEndDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() + (6 - today.getDay()));
    }
  
   
    const results = await this.aggregate([
      {
        $match: {
          ...filters,
          createdAt: {
            $gte: WeekStartDate,
            $lt: WeekEndDate,
          },
        },
      },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
          count: { $sum: 1 },
          totalCoins: { $sum: '$coins' },
        },
      },
      {
        $sort: { _id: 1 },
      },
    ]);
  
    console.log(results)
    // Fill in missing months with zero completions
    const resultWithZeros = [];
    for (let i = 1; i <= 7; i++) {
      resultWithZeros.push({day: daysOfWeek[i-1], count: 0, totalCoins: 0});
    }
    (results || []).forEach(({_id, count, totalCoins}) => {
      const date = new Date(_id);
      const day = date.getDay();
      resultWithZeros[day] = {day:daysOfWeek[day], count, totalCoins};
    });
    return resultWithZeros;
  };
  

const CoinWalletTransaction = mongoose.model('CoinWalletTransaction', coinWalletTransactionSchema);
module.exports = {
    CoinWalletTransaction
};
