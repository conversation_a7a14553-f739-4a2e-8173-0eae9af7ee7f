const express = require('express');
const router = express.Router();

const validate = require('../../../middlewares/validate');
const { adminProtect } = require('../../../middlewares/adminAuth');
// const blogValidation = require('../../../validations/blog.validation');

const coinOfferController = require('../../../controllers/admin/coinOffer.controller');


router.post(
    '/add',
    adminProtect,
    // validate(blogValidation.AddBlog),
    coinOfferController.store
);

router.post(
    '/update',
    adminProtect,
    // validate(blogValidation.EditBlog),
    coinOfferController.updateCoinOffer
)

router.get(
    '/list',
    adminProtect,
    coinOfferController.getCoinOffers
);
router.get(
    '/:id',
    adminProtect,
    coinOfferController.getCoinOffer
);

router.delete(
    '/:id',
    adminProtect,
    coinOfferController.deleteCoinOffer
);

module.exports = router;