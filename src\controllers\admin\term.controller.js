const catchAsync = require("../../utils/catchAsync");
const termService = require("../../services/term.service");
const ApiError = require("../../utils/ApiError");
const httpStatus = require("http-status");

const getTerms = catchAsync(async (req, res, next) => {
    try {
        const terms = await termService.getTerms();
        res.status(200).send({ data: terms, message: '' });
    } catch (error) {
        next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing Terms', error));
    }
});

const getTerm = catchAsync(async (req, res, next) => {
        const term = await termService.getTermById(req.params.id);
        res.status(200).send({ data: term, message: '' });
});

const store = catchAsync(async (req, res, next) => {

    const term = await termService.createTerm(req.body);
    res.status(201).send({ data: term, message: 'term is created Successfully' });

});

const updateTerm = catchAsync(async (req, res, next) => {

    const term = await termService.updateTermById(req.body._id, req.body);
    res.status(200).send({ data: term, message: 'term is updated Successfully' });

});

module.exports = {
    store,
    getTerms,
    updateTerm,
    getTerm
}