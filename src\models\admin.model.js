const mongoose = require('mongoose');
const {paginate} = require('./plugins/paginate');

const adminSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      trim: true,
      required: true,
    },
    email: {
      type: String,
      trim: true,
      required: true,
    },
    password:{
        type: String,
        trim: true,
        required: true,
    },
    profilePic: {
      type: {
        key: String,
        url: String,
      },
      default: null,
    },
  },
  {timestamps: true}
);

adminSchema.plugin(paginate);

const Admin = mongoose.model('admin', adminSchema);
module.exports = {
  Admin
};
