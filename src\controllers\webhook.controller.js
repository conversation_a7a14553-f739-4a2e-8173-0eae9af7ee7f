const catchAsync = require("../utils/catchAsync");
const { Payment } = require("../models/payment.model");
const {
  CoinWalletTransaction,
} = require("../models/coinWalletTransaction.model");
const { userCoinWalletService } = require("../services");
const config = require("../config/config");
const { CoinOffer } = require("../models/coinOffer.model");
const stripe = require("stripe")(config.stripe.secret);

const handleStripeEvent = catchAsync(async (req, res) => {
  const sig = req.headers["stripe-signature"];
  const body = req.body;
  try {
    const event = stripe.webhooks.constructEvent(
      req.rawBody,
      sig,
      config.stripe.WEBHOOK_SECRET
    );
    console.log(event);
    if (event.object !== "event") {
    } else {
      if (
        [
          "checkout.session.expired",
          "checkout.session.async_payment_failed",
        ].includes(event.type)
      ) {
      } else if (event.type === "checkout.session.completed") {
        const { id, payment_status, payment_intent } = event.data.object;

        const payment = await Payment.findOne({ sessionId: id });
        payment.payment_status = payment_status;
        payment.payment_intent = payment_intent;
        payment.status = "completed";
        await payment.save();

        const wallet = await userCoinWalletService.createAndGetWallet(
          payment.user
        );

        const coinWalletTransaction = await CoinWalletTransaction.create({
          userId: payment.user,
          walletId: wallet._id,
          transactionType: "credit",
          coinOffer: payment.metadata?.coinOffer,
          coins: payment.coins,
          balanceBefore: wallet.balance,
          balanceAfter: wallet.balance + payment.coins,
          description: "",
          paymentId: payment._id,
        });

        wallet.balance = coinWalletTransaction.balanceAfter;
        await wallet.save();

        if (payment.metadata && payment.metadata.type == "coinOffer") {
          const coinOffer = await CoinOffer.findById(
            payment.metadata.coinOffer
          );
          coinOffer.totalSold = coinOffer.totalSold + 1;
          await coinOffer.save();
        }
      }
    }
    res.status(200).send({ status: true });
  } catch (err) {
    console.log("handleStripeEvent ~ err:", err);
    res.status(400).send(`Webhook Error: ${err.message}`);
  }
});

module.exports = {
  handleStripeEvent,
};
