const catchAsync = require("../utils/catchAsync");
const commentService = require("../services/comment.service");
const postService = require("../services/post.service");

const ApiError = require("../utils/ApiError");
const httpStatus = require("http-status");
const userNotificationModel = require("../models/userNotification.model");
const notiFunc = require('../microservices/notification.service');

const getComments = catchAsync(async (req, res, next) => {

    try {
        const { postId } = req.params;
        const comments = await commentService.getComments({ postId: postId });
        res.status(200).send({ data: comments, message: '' });
    } catch (error) {
        next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing Terms', error));
    }
});

const getComment = catchAsync(async (req, res, next) => {
    const comment = await commentService.getCommentById(req.params.id);
    res.status(200).send({ data: comment, message: '' });
});

const store = catchAsync(async (req, res, next) => {
    const user = req.user;

    const post = await postService.getPostById(req.body.postId);

    if(!post){
        throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid Post');
    }

    const comment = await commentService.createComment({ ...req.body, userId: user._id });

    post.commentCount = post.commentCount + 1;
    await post.save();

    const data = {
        receiver:post.userId,
        title: `New comment on your post.`,
        description: `${user.name} has commented on your post.`,
    };

    const userNotifications = await userNotificationModel.create(data);

    await notiFunc.sendToTopic(post.userId?._id.toString(), { title: data.title, body: data.description });

    res.status(201).send({ data: comment, message: 'Comment is created Successfully' });
});

const updateComment = catchAsync(async (req, res, next) => {
    const user = req.user;
    const commentObj = await commentService.getCommentById(req.body._id);

    // console.log("---commentObj.userId---", commentObj.userId);
    // console.log("---user._id---", user._id);
    if (commentObj && commentObj.userId.toString() == user._id.toString()) {
        const comment = await commentService.updateCommentById(req.body._id, { ...req.body, userId: user._id });
        res.status(200).send({ data: comment, message: 'Comment is updated Successfully' });
    }
    else {
        throw new ApiError(httpStatus.FORBIDDEN, 'Unthorized user');
    }
});

const deleteComment = catchAsync(async (req, res, next) => {
    const user = req.user;
    const commentObj = await commentService.getCommentById(req.params.id);

    if (commentObj && commentObj.userId.toString() == user._id.toString()) {
        const post = await postService.getPostById(commentObj.postId);
        if(post){
            throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid Post');
        }
        const comment = await commentService.deleteCommentById(req.params.id);
        post.commentCount = post.commentCount - 1;
        await post.save();
        res.status(200).send({ data: comment, message: 'Comment is deleted Successfully' });
    }
    else {
        throw new ApiError(httpStatus.FORBIDDEN, 'Unthorized user');
    }
});


module.exports = {
    store,
    getComments,
    getComment,
    updateComment,
    deleteComment
}