const cors = require("cors");
const express = require("express");
const compression = require("compression");
const helmet = require("helmet");
const httpStatus = require("http-status");
const routes = require("./routes/v1");
const morgan = require("./config/morgan");
const config = require("./config/config");
const ApiError = require("./utils/ApiError");
const { errorConverter, errorHandler } = require("./middlewares/error");
const http = require("http");
const { initializeSocket } = require("./config/socketIO");

const app = express();
require("./events");

// Create an HTTP server
const server = http.createServer(app);

// Attach Socket.IO to the HTTP server
initializeSocket(server);

// <PERSON> will handle logging HTTP requests, while <PERSON> logger will take care of your application-specific logs
if (config.env !== "test") {
  app.use(morgan.successHandler);
  app.use(morgan.errorHandler);
}

// Set security HTTP headers
app.use(helmet());

// Parse raw JSON bodies
app.use(
  express.json({
    limit: "5mb",
    verify: (req, res, buf) => {
      req.rawBody = buf.toString();
    },
  })
);

// Parse JSON and URL-encoded request bodies
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Gzip compression
app.use(compression());

// Enable CORS
app.use(cors());
app.options("*", cors());

// Reroute all API requests starting with "/v1" route
app.use("/v1", routes);

// Send back a 404 error for any unknown API request
app.use((req, res, next) => {
  next(new ApiError(httpStatus.NOT_FOUND, "Not found"));
});

// Convert error to ApiError, if needed
app.use(errorConverter);

// Handle error
app.use(errorHandler);

// Attach the Socket.IO instance to the app object for global use

// Export both the app and the HTTP server
module.exports = { app, server };
