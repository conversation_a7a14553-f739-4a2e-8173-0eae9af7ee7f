const catchAsync = require("../utils/catchAsync");
const User = require("../models/user.model");
const config = require("../config/config");
const customerService = require("../services/customer.service");
const CryptoJS = require("crypto-js");
const eventEmitter = require("../events/eventEmitter");
const { emitterEventNames } = require("../constants");

const stripe = require("stripe")(config.stripe.secret);

exports.addCard = async (user, code) => {
  try {
    const bytes = CryptoJS.AES.decrypt(code, "returnzIU");
    const originalText = bytes.toString(CryptoJS.enc.Utf8);
    const body = JSON.parse(originalText.replace(/\//g, ""));

    let cusId;
    let StripeCustomer;

    const {
      card_country,
      card_holder,
      card_number,
      card_date,
      card_cvc,
    } = body;

    const card_exp = card_date.split("-");

    if (user.stripeCustomerId != null) {
      cusId = user.stripeCustomerId;
    } else {
      StripeCustomer = await stripe.customers.create({ email: user.email });
      cusId = StripeCustomer.id;
      // console.log(user);
      const update_user = await User.findByIdAndUpdate(
        user._id,
        {
          stripeCustomerId: StripeCustomer.id,
        },
        { new: true }
      );

      // Emit event for assistant update
      if (update_user) {
        eventEmitter.emit(emitterEventNames.ASSISTANT_UPDATE, update_user);
      }
    }

    var param = {};
    param.card = {
      address_country: card_country,
      name: card_holder,
      number: card_number,
      exp_month: card_exp[0],
      exp_year: card_exp[1],
      cvc: card_cvc,
    };

    const StripeToken = await stripe.tokens.create(param);
    const addCardToCustomer = await stripe.customers.createSource(cusId, {
      source: StripeToken.id,
    });
    const update_user = await User.findByIdAndUpdate(
      user._id,
      {
        stripeCardId: addCardToCustomer.id,
      },
      { new: true }
    );

    // Emit event for assistant update
    if (update_user) {
      eventEmitter.emit(emitterEventNames.ASSISTANT_UPDATE, update_user);
    }

    return {
      status: true,
      data: addCardToCustomer,
    };
  } catch (err) {
    return {
      status: false,
      msg: err.message,
    };
  }
};

const IntentPayment = async (user, body, currency = null) => {
  try {
    console.log(body);
    const paymentIntent = await stripe.paymentIntents.create({
      amount: (body.amount * 100).toFixed(0),
      currency: "usd",
      payment_method_types: ["card"],
      customer: user.stripeCustomerId,
      payment_method: body.card ? body.card : user.stripeCardId,
      confirm: true,
    });

    console.log(paymentIntent);
    return {
      status: true,
      data: paymentIntent,
    };
  } catch (err) {
    console.log(err);
    return {
      status: false,
      msg: err.message,
    };
  }
};

exports.ChargeAmount = async (amount, card_id, user) => {
  let transactionId = "";
  let paymentDetail = {};
  if (amount <= 0) {
    return {
      status: false,
      msg: "Error: Invalid amount. Please enter an amount greater than zero.",
    };
  }
  console.log(amount);

  //new line added
  const stripeRes = await IntentPayment(user, {
    amount: amount,
    card: card_id,
  });
  if (stripeRes.status == false) {
    return stripeRes;
  }

  paymentDetail = stripeRes.data;
  transactionId = paymentDetail.id;

  return {
    status: true,
    paymentDetail,
    transactionId,
  };
};

getCardId = async (user, limit) => {
  const cards = await stripe.customers.listSources(user.stripeCustomerId, {
    object: "card",
    limit: limit,
  });

  return cards.data[0].id;
};

exports.refundPayment = async (paymentIntentId) => {
  try {
    const refund = await stripe.refunds.create({
      payment_intent: paymentIntentId,
    });
    console.log("Payment refunded:", refund.id);
    return {
      status: true,
      refundId: refund.id,
    };
  } catch (error) {
    console.error("Refund failed:", error);
    return {
      status: false,
      msg: err.message,
    };
  }
};

// subscription's methods
exports.createStripeProductAndPrice = async (body) => {
  try {
    const product = await stripe.products.create({
      name: body.title,
    });
    console.log("product", product);

    const stripePrice = [];

    await Promise.all(
      body.stripePrice.map(async (element) => {
        const price = await stripe.prices.create({
          unit_amount: (element.price * 100).toFixed(),
          currency: "mxn",
          recurring: { interval: element.duration },
          product: product.id,
        });

        stripePrice.push({
          id: price.id,
          amount: element.price,
          duration: element.duration,
        });
      })
    );

    return {
      status: true,
      product: product,
      stripePrice,
    };
  } catch (err) {
    console.log(err);
    return {
      status: false,
      message: err.message,
    };
  }
};

// subscription's methods
exports.updateStripeProductAndPrice = async (body) => {
  try {
    const product = await stripe.products.update(body.stripeProductId, {
      name: body.title,
    });
    console.log("product", product);

    const stripePrice = [];

    await Promise.all(
      body.stripePrice.map(async (element) => {
        console.log(element);
        const price = await stripe.prices.update(element.id, {
          unit_amount: (element.price * 10).toFixed(),
          currency: "usd",
          recurring: { interval: element.duration },
        });

        stripePrice.push({
          id: price.id,
          amount: element.price,
          duration: element.duration,
        });
      })
    );

    return {
      status: true,
      product: product,
      stripePrice,
    };
  } catch (err) {
    console.log(err);
    return {
      status: false,
      message: err.message,
    };
  }
};

// add subscriptions
exports.createSubscriptionForUser = async (
  stripeCustomerId,
  cardId,
  stripePriceId,
  time
) => {
  try {
    console.log(time);
    // // for production
    // const subscription = await stripe.subscriptions.create({
    // 	customer: stripeCustomerId,
    // 	default_payment_method: cardId,
    // 	items: [{ price: stripePriceId}],
    // 	trial_period_days: 180,
    // });

    // for dev test
    let trial_period_days = 0;
    if (time == "monthly") {
      trial_period_days = 30;
    } else if (time == "yearly") {
      trial_period_days = 365;
    }

    // const trialEndDate = moment().add(3, 'minutes');

    const subscription = await stripe.subscriptions.create({
      customer: stripeCustomerId,
      default_payment_method: cardId,
      items: [{ price: stripePriceId }],
      trial_period_days: trial_period_days,
      // trial_end: trialEndDate.unix(), // Convert trial end time to Unix timestamp
    });

    return {
      status: true,
      subscription: subscription,
    };
  } catch (err) {
    console.log(err);
    return {
      status: false,
      message: err.message,
    };
  }
};

exports.createSubscriptionCheckout = async (user, lineItems, mode, host) => {
  let customerId = user.stripeCustomerId;
  if (!customerId) {
    customerId = await customerService.createCustomer(user._id);
  }
  console.log(host);
  try {
    const data = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ["card"],
      line_items: lineItems,
      mode: mode,
      success_url: `${host}/PaymentSuccess`,
      cancel_url: `${host}/PaymentCancelled`,
    });
    console.log("data:", data);
    return {
      status: true,
      data: data,
    };
  } catch (error) {
    console.log("error:", error);
    return { status: false, message: "Failed to create payment" };
  }
};

// get upcoming invoice
exports.getStripeUpcomingInvoice = async (customer) => {
  try {
    const invoice = await stripe.invoices.retrieveUpcoming({
      customer: customer,
    });
    return {
      status: true,
      invoice: invoice,
    };
  } catch (err) {
    return {
      status: false,
      message: err.message,
    };
  }
};

// cancel subscription
exports.cancelSubscription = async (subcriptionId) => {
  try {
    const deleted = await stripe.subscriptions.cancel(subcriptionId);
    return {
      status: true,
      data: deleted,
    };
  } catch (err) {
    return {
      status: false,
      message: err.message,
    };
  }
};

// cancel subscription
exports.updateSubscription = async (subcriptionId, price) => {
  try {
    const subscriptionItem = await getSubscriptionItemList(subcriptionId);

    if (subscriptionItem.data.length == 0) {
      return {
        status: false,
        message: "no price found",
      };
    }

    const subscriptionItemId = subscriptionItem.data[0].id;
    const updatedSubscriptionItem = await stripe.subscriptionItems.update(
      subscriptionItemId,
      {
        price: price,
        proration_behavior: "none",
        // billing_cycle_anchor: 'now'
      }
    );
    console.log(updatedSubscriptionItem);
    const update = await stripe.subscriptions.update(subcriptionId, {
      proration_behavior: "none",
      billing_cycle_anchor: "now",
    });
    console.log(update);
    return {
      status: true,
      data: update,
    };
  } catch (err) {
    return {
      status: false,
      message: err.message,
    };
  }
};

const getSubscriptionItemList = async (subcriptionId) => {
  const subscriptionItems = await stripe.subscriptionItems.list({
    limit: 1,
    subscription: subcriptionId,
  });

  return subscriptionItems;
};

// make card default
exports.makeCardDefault = async (customerId, newDefaultCardId) => {
  try {
    // Retrieve the customer from Stripe
    // const customer = await stripe.customers.retrieve(customerId);
    // customer.default_source = newDefaultCardId;
    const updatedCustomer = {
      default_source: newDefaultCardId,
    };
    await stripe.customers.update(customerId, updatedCustomer);

    console.log("Default card updated successfully.");
    return true;
  } catch (err) {
    console.error("Error setting default card:", err.message);
    return false;
  }
};

// check a payment card is already added or not
const checkCardAlreadyAdded = async (customerId, cardToCheck) => {
  try {
    const paymentMethods = await stripe.paymentMethods.list({
      customer: customerId,
      type: "card",
    });

    const isCardAlreadyAdded = paymentMethods.data.find((paymentMethod) => {
      if (
        paymentMethod.card.last4 === cardToCheck.card_number.slice(-4) &&
        paymentMethod.card.exp_month.toString() ===
          cardToCheck.card_date.split("-")[0] &&
        paymentMethod.card.exp_year.toString().slice(-2) ===
          cardToCheck.card_date.split("-")[1]
      ) {
        return paymentMethod;
      }
    });
    return {
      status: true,
      data: isCardAlreadyAdded,
    };
  } catch (err) {
    return {
      status: false,
      message: err.message,
    };
  }
};

exports.deleteCardStripe = async (user, stripecardId) => {
  const customerSource = await stripe.customers.deleteSource(
    user,
    stripecardId
  );
  return customerSource;
};

exports.changeCard = async (subscriptionId, cardId) => {
  const subscription = await stripe.subscriptions.update(subscriptionId, {
    default_payment_method: cardId,
  });
  return subscription;
};

exports.subcriptionDetail = async (subscriptionId) => {
  try {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);

    return {
      status: true,
      data: subscription,
    };
  } catch (error) {
    return {
      status: false,
    };
  }
};
