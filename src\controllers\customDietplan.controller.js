const ApiError = require("../utils/ApiError");
const httpStatus = require("http-status");
const catchAsync = require("../utils/catchAsync");
const { CustomDietPlan } = require("../models/customDietPlanModel");
const { vectorStoreService } = require("../aiServices");
const { CustomWorkoutPlan } = require("../models/customWorkoutModel");
const moment = require("moment");

const {
  scheduleNextMealNotification,
  cancelExistingNotifications,
} = require("../utils/helper");

const { processDietPlan } = require("../utils/customPlanHandler");
const {
  uploadFileToVectorStore,
} = require("../aiServices/vectorStore.service");

const createDietPlan = catchAsync(async (req, res) => {
  const {
    dietPlan,
    breakfastTime,
    lunchTime,
    dinnerTime,
    startDate,
  } = req.body;
  const user = req.user;

  if (!dietPlan || !breakfastTime || !lunchTime || !dinnerTime) {
    return res.status(400).json({ message: "Missing required fields" });
  }
  const existingDietPlan = await CustomDietPlan.find({ user: user._id });

  // Remove existing diet plan for the user
  await CustomDietPlan.deleteMany({ user: user._id });

  // Use the provided startDate or default to today
  const startDateObj = new Date(startDate || new Date());

  // Create a formatted diet plan with dates instead of days
  const formattedDietPlan = dietPlan.map((day, index) => {
    const currentDate = new Date(startDateObj);
    currentDate.setDate(startDateObj.getDate() + index); // Calculate date for each day
    return {
      date: currentDate.toISOString().split("T")[0], // Format date as YYYY-MM-DD
      breakfast: day.breakfast || null,
      lunch: day.lunch || null,
      dinner: day.dinner || null,
      isLeave: day.isLeave || false,
    };
  });

  // Calculate the end date
  const endDateObj = new Date(startDateObj);
  endDateObj.setDate(startDateObj.getDate() + dietPlan.length - 1);

  const formattedEndDate = endDateObj.toISOString().split("T")[0];

  // Save new diet plan
  const customDietPlan = new CustomDietPlan({
    user: user._id,
    endDate: formattedEndDate,
    dietPlan: formattedDietPlan,

    notificationSchedule: {
      enabled: true,
      breakfastTime,
      lunchTime,
      dinnerTime,
    },
  });

  await customDietPlan.save();

  const jsonlData = await processDietPlan(
    formattedDietPlan,
    user._id,
    user.name
  );
  if (jsonlData) {
    const uploadResult = await uploadFileToVectorStore(
      jsonlData,
      user._id,
      user.name,
      "diet",
      existingDietPlan[0]
    );

    if (uploadResult) {
      // Update vectorFileId
      const updatedPlan = await CustomDietPlan.findOneAndUpdate(
        { user: user._id },
        { vectorFileId: uploadResult.fileId },
        { new: true }
      );

      if (updatedPlan) {
        const vectorStore = await vectorStoreService.getOrCreateVectorStore();

        // Fetch the user's diet plan vector file (if it exists)
        const workoutPlan = await CustomWorkoutPlan.findOne({
          user: user._id,
        }).select("vectorFileId");
        const workoutPlanFileId = workoutPlan?.vectorFileId || "not avaliable";

        console.log("📢 Updating assistant with new diet vector file...");

        // console.log(
        //   uploadResult.fileId,
        //   dietPlanFileId,
        //   "<<<<<<<<<<<<<<<<<<<<<<<<<<<"
        // );

        const instructions = vectorStoreService.createInstructions(
          user,
          workoutPlanFileId,
          uploadResult.fileId
        );

        await vectorStoreService.updateAssistant(
          user.assistantId,
          vectorStore.vectorStoreId,
          instructions,
          user._id
        );

        console.log("✅ Assistant updated with new diet plan.");
      }
      // Add a delay to handle potential replication issues
    }
  }

  await cancelExistingNotifications(user._id);

  await scheduleNextMealNotification(
    user._id,
    customDietPlan.dietPlan,
    customDietPlan.notificationSchedule
  );

  res.status(200).json({
    message: "Diet Plan created successfully",
    data: customDietPlan,
  });
});

const updateDietPlan = catchAsync(async (req, res) => {
  const { breakfastTime, lunchTime, dinnerTime } = req.body;
  const user = req.user;

  if (!breakfastTime || !lunchTime || !dinnerTime) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Missing required fields");
  }

  // Find the existing diet plan
  const dietPlan = await CustomDietPlan.findOne({
    user: user._id,
    isFinished: false,
  });

  if (!dietPlan) {
    throw new ApiError(httpStatus.BAD_REQUEST, "No active diet plan found");
  }

  // Update the notification times
  dietPlan.notificationSchedule = {
    enabled: true,
    breakfastTime,
    lunchTime,
    dinnerTime,
  };

  await dietPlan.save();

  console.log(`✅ Updated diet plan times for user: ${user._id}`);

  // Cancel existing notifications
  await cancelExistingNotifications(user._id);

  // Reschedule the next meal with new times
  await scheduleNextMealNotification(dietPlan);

  res.status(200).json({
    message: "Diet Plan updated successfully",
    data: dietPlan,
  });
});

const getCustomDietPlan = catchAsync(async (req, res) => {
  const user = req.user;
  const dietPlan = await CustomDietPlan.findOne({ user: user._id })
    .populate({
      path: "dietPlan.breakfast._id", // Populate breakfast meal's recipe
    })
    .populate({
      path: "dietPlan.lunch._id", // Populate lunch meal's recipe
    })
    .populate({
      path: "dietPlan.dinner._id", // Populate dinner meal's recipe
    })
    .lean();

  if (!dietPlan) {
    return res.status(404).json({ message: "No diet plan found" });
  }

  // If a specific date is requested
  if (req.query.date) {
    const requestedDate = moment(req.query.date, "DD-MM-YYYY").startOf("day");

    // Parse and compare dates correctly
    const dayPlan = dietPlan.dietPlan.find((day) => {
      const dayDate = moment(day.date).startOf("day"); // Ensure `day.date` is parsed correctly
      return dayDate.isSame(requestedDate, "day");
    });

    if (!dayPlan) {
      return res
        .status(400)
        .json({ message: "No diet plan found for the requested date" });
    }

    return res.status(200).json({
      message: `Diet Plan for ${req.query.date}`,
      data: dayPlan,
    });
  }

  // Return the full diet plan if no specific date is requested
  res.status(200).json({
    message: "Diet Plan retrieved successfully",
    data: dietPlan,
  });
});

module.exports = { createDietPlan, updateDietPlan, getCustomDietPlan };
