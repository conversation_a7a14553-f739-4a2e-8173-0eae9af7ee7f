const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const catchAsync = require('../utils/catchAsync');
const { dietPlanService, dietLogService, dietCategoryService } = require('../services');
const { DietCategory } = require('../models/dietCategory.model');
const { getPaginateConfig } = require('../utils/queryPHandler');

const getAlldietPlan = catchAsync(async (req, res) => {

    const user = req.user;

    const {filters,options} = getPaginateConfig(req.query);

    if (filters.search) {
       filters.$or = [
            { name: { $regex: req.query.search, $options: 'i' } }, // Case-insensitive match for 'name'
            { description: { $regex: req.query.search, $options: 'i' } }, // Case-insensitive match for 'description'
            // Add more fields as needed for the keyword search
        ];

        delete filters.search;
    }

    const dietPlans = await dietPlanService.getAllDietPlanWithCustomFilters(filters, options)
    res.status(200).json({
        message: "All diet plan",
        data: dietPlans
    })
});

const getdietPlanById = catchAsync(async (req, res) => {
    const dietPlan = await dietPlanService.viewDietPlan(req.params.id,req.query.day)
    res.status(200).json({
        message: "Work out plan details",
        data: dietPlan
    })
});


const addDietLog = catchAsync(async (req, res) => {

    const body = req.body;
    const user = req.user;

    const recipe = await dietPlanService.viewRecipe(body.recipeId);

    const data = {
        ...body,
        userId:user._id,
        Kcal: recipe.nutritionalValue.Kcal,
        protien:recipe.nutritionalValue.Protein,
        carbs:recipe.nutritionalValue.Carbs,
        foodName:recipe.name,
        type:"recipe"
    };
    const dietPlan = await dietLogService.addDietLog(data);
    res.status(200).json({
        message: "Work out plan details",
        data: dietPlan
    })
});

const addCustomDietLog = catchAsync(async (req, res) => {

    const body = req.body;
    const user = req.user;

    const data = {
        ...body,
        userId :user._id,
        type:"custom"
    };
    const dietPlan = await dietLogService.addDietLog(data);
    res.status(200).json({
        message: "Work out plan details",
        data: dietPlan
    })
});


const getDietLog = catchAsync(async (req, res) => {

    const user = req.user;

    const dietPlan = await dietLogService.getUserdietLog(user._id);
    res.status(200).json({
        message: "Work out plan details",
        data: dietPlan
    })
});

const getDietCategories = catchAsync(async (req, res) => {

    const user = req.user;

    const dietCategories = await DietCategory.find({});
    res.status(200).json({
        message: "",
        data: dietCategories,
        status:true
    })
});



module.exports = {
    getAlldietPlan,
    getdietPlanById,
    addDietLog,
    getDietLog,
    addCustomDietLog,
    getDietCategories
}