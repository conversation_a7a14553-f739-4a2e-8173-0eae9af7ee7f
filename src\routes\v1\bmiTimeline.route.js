const express = require('express');
const router = express.Router();
const { firebaseAuth } = require('../../middlewares/firebaseAuth');
// const validate = require('../../../middlewares/validate');
// const { adminProtect } = require('../../../middlewares/adminAuth');
// const equipmentValidation = require('../../../validations/equipment.validation');

const bmiTimelineController = require('../../controllers/bmiTimeline.controller');
const { fileUploadService } = require('../../microservices');


router.post(
    '/add',
    fileUploadService.multerUpload.single('image'),
    firebaseAuth,
    // validate(equipmentValidation.AddEquipment),
    bmiTimelineController.store
);

router.post(
    '/update',
    fileUploadService.multerUpload.single('image'),
    firebaseAuth,
    // validate(equipmentValidation.EditEquipment),
    bmiTimelineController.updateBmiTimeline
);

router.get(
    '/list/',
    firebaseAuth,
    bmiTimelineController.getBmiTimelines
);
router.get(
    '/:id',
    firebaseAuth,
    bmiTimelineController.getBmiTimeline
);

router.delete(
    '/:id',
    firebaseAuth,
    bmiTimelineController.deleteBmiTimeline
);

module.exports = router;
