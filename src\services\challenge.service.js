const { User, Client } = require('../models');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { fileUploadService } = require('../microservices');
const { Challenge } = require('../models/challenge.model');

async function getChallengeById(id) {
    const challenge = await Challenge.findById(id);
    return challenge;
}

async function getChallenges(filter = {}, keyword = '', timeFrame = 'all', options = {}) {
    try {

        // Filtering based on keyword (if provided)
        if (keyword) {
            filter.$or = [
                { title: { $regex: keyword, $options: 'i' } }, // Case-insensitive match for 'title'
                { description: { $regex: keyword, $options: 'i' } }, // Case-insensitive match for 'description'
                // Add more fields as needed for the keyword search
            ];
        }

        // Apply filters to the aggregation pipeline
        let pipeline = [];

        // Filtering based on timeFrame
        if (timeFrame !== 'all') {
            const now = new Date();
            let startDate;
            switch (timeFrame) {
                case 'today':
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    break;
                case 'thisWeek':
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay());
                    break;
                case 'thisMonth':
                    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                    break;
                case 'thisYear':
                    startDate = new Date(now.getFullYear(), 0, 1);
                    break;
                default:
                    startDate = new Date(0); // To include all dates (past and present)
            }
            filter.createdAt = { $gte: startDate, $lte: now };
        }
        console.log(filter);
        // Aggregate query
        const challenge = await Challenge.paginate(filter, options);
        return challenge;
    } catch (error) {
        console.log(error);
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing challenges');
    }
}

async function createChallenge(details, media) {
    let data = { ...details };
    const image = media.thumbnail;
    const workoutVideo = media.refVideo;

    if (image) {
        const [thumbnail] = await fileUploadService.s3Upload(image, 'thumbnail').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload thumbnail');
        })
        data = { ...data, thumbnail };
    };

    if (workoutVideo) {
        const [refVideo] = await fileUploadService.s3Upload(workoutVideo, 'refVideo').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload video');
        })
        data = { ...data, refVideo };
    };

    return await Challenge.create(data);
}

async function updateChallengeById(id, body, media = {}) {
    const exercise = await Challenge.findById(id);

    const Image = media.thumbnail;
    const workoutVideo = media.refVideo;
    let updates = { ...body };

    if (Image) {
        const [thumbnail] = await fileUploadService.s3Upload(Image, 'thumbnail').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload thumbnail');
        });
        if (exercise.thumbnail) {
            const oldPicKey = exercise.thumbnail.key;
            await fileUploadService
                .s3Delete(oldPicKey)
                .catch(err => console.log('Failed to delete image', oldPicKey));
        }
        updates = { ...updates, thumbnail };
    }
    if (workoutVideo) {
        const [refVideo] = await fileUploadService.s3Upload(workoutVideo, 'refVideo').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload video');
        });
        if (exercise.refVideo) {
            const oldPicKey = exercise.refVideo.key;
            await fileUploadService
                .s3Delete(oldPicKey)
                .catch(err => console.log('Failed to delete video', oldPicKey));
        }

        updates = { ...updates, refVideo };
    }
    return await Challenge.findByIdAndUpdate(id, updates, { new: true });
}

async function deleteChallengeById(id) {
    try {
        await Challenge.findByIdAndDelete(id);
        return true;
    } catch (err) {
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to delete the challenge');
    }
}

// get all workout plans
async function getAllChallengeWithCustomFilters(type, events) {

    // if (type == "Upcoming") {
    //     const events = events.filter(event => event.startDate > currentDate);
    // }
    // elseif(type == "Ongoing"){
    //     const events = events.filter(event => event.startDate <= currentDate && event.endDate >= currentDate);
    // }
    // elseif(type == "Past"){
    //     const events = events.filter(event => event.endDate < currentDate);
    // }


    return events;
}

module.exports = {
    getChallengeById,
    getChallenges,
    createChallenge,
    updateChallengeById,
    deleteChallengeById,
    getAllChallengeWithCustomFilters
}