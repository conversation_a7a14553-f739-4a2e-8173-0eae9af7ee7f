const mongoose = require('mongoose');
const { paginate } = require('./plugins/paginate');

const notificationSchema = new mongoose.Schema(
    {
        title: {
            type: String,
            required: true
        },
        description: {
            type: String,
            required: true
        },
        receiver: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "User"
        },
        isForAll: {
            type: Boolean,
            default: true
        },
        scheduledAt: {
            type: Date,
        },
        isScheduled: {
            type: Boolean,
            default: false
        },
        isDelivered:{
            type: Boolean,
            default: false
        }
    },
    { timestamps: true }
);

notificationSchema.plugin(paginate);

const Notification = mongoose.model('Notification', notificationSchema);
module.exports = {
    Notification
};