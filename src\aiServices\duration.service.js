const openai = require("../config/openAi");
const ApiError = require("../utils/ApiError");
const httpStatus = require("http-status");

async function getDurationFromQuery(query) {
  const systemPrompt = `
      You are an intelligent assistant designed to analyze user queries related to health, fitness, meals, workouts, diet plans, and general well-being. Your task is to classify the query, identify the focus (workout, meal, or both), and extract the duration.
  
      ### Instructions:
      1. **Classify the Query**:
      - **Workout-only**: If the query is exclusively about physical activities (e.g., muscle building, strength training, exercise routines, or specific workout plans), assign the duration to \`workout_duration\`, and set \`meal_duration = 0\`.
          - Example: "I want to build muscle over the next 3 months."
          - Result: \`{"meal_duration": 0, "workout_duration": 90}\`
  
      - **Meal-only**: If the query is focused solely on meal plans or diet (e.g., weight loss, meal planning, or nutrition advice), assign the duration to \`meal_duration\`, and set \`workout_duration = 0\`.
          - Example: "I need a calorie-restricted meal plan to gain muscle for 3 months"
          - Result: \`{"meal_duration": 90, "workout_duration": 0}\`
  
      - **Both**: If the query asks for both a workout and a meal plan (e.g., a diet and exercise plan), assign durations to both \`meal_duration\` and \`workout_duration\`.
          - Example: "I need a 1-month diet and workout plan."
          - Result: \`{"meal_duration": 30, "workout_duration": 30}\`
  
      - **General health**: If the query is vague or about overall health, fitness, or well-being without specifying a workout or meal plan, assign **default durations** of \`meal_duration = 30\` and \`workout_duration = 30\`.
    `;

  try {
    // Interact with OpenAI API
    const response = await openai.chat.completions.create({
      model: "gpt-4.1", // Update with correct model version
      messages: [
        { role: "system", content: systemPrompt.toString() },
        { role: "user", content: query },
      ],
    });

    const gptResponse = response.choices[0].message.content.trim();
    console.log("GPT Response:", gptResponse);

    try {
      return JSON.parse(gptResponse);
    } catch (jsonError) {
      console.warn("Direct JSON parse failed. Attempting regex extraction...");

      // Fallback: Use regex to capture JSON-like content
      const match = gptResponse.match(/\{[\s\S]*?\}/); // Matches JSON-like structure
      if (match) {
        return JSON.parse(match[0]); // Parse the extracted JSON
      } else {
        throw new Error("Failed to extract valid JSON from response.");
      }
    }
  } catch (error) {
    throw new Error(`Failed to extract duration : ${error.message}`);
  }
}

module.exports = { getDurationFromQuery };
