const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const catchAsync = require('../utils/catchAsync');
const { challengeService } = require('../services');

const getAllChallenges = catchAsync(async (req, res) => {

    const type = req.params.type

    let filter = {};

    const currentDate = new Date();

    if (type == "upcoming") {
        filter.startDate = { $gt: currentDate };
    }
    else if (type == "ongoing") {
        filter.startDate = { $lte: currentDate };
        filter.endDate = { $gte: currentDate };
        filter.IsEnded = false
    }
    else if (type == "Past") {
        filter.$or= [
            { endDate : { $lt: currentDate }},
            {IsEnded: true}
        ];
        
    }

    const allChallenges = await challengeService.getChallenges(filter);
    res.status(200).json({
        message: "All challenges",
        data: allChallenges
    })
});

const getChallengeById = catchAsync(async (req, res) => {
    const workoutPlan = await challengeService.getChallengeById(req.params.id)
    res.status(200).json({
        message: "Work out plan details",
        data: workoutPlan
    })
});

module.exports = {
    getAllChallenges,
    getChallengeById
}