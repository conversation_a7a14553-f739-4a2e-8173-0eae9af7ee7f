const mongoose = require('mongoose');
const { paginate } = require('./plugins/paginate');

const workoutPlanSchema = new mongoose.Schema(
    {
        thumbnail: {
            type: {
                key: String,
                url: String,
            },
            default: null,
        },
        name: {
            type: String,
            default: null,
        },
        description: {
            type: String,
            default: null,
        },
        duration: {
            type: Number,
            default: null,
        },
        caloriesBurnt: {
            type: Number,
            default: null,
        },
        idealFor: {
            type: String,
            default: null,
            enum: ["Beginner", "Normal", "Medium", "Advanced"]
        },
        type: {
            type: String,
            default: null,
            enum: ["StrengthTraining", "Yoga", "Cardio"]
        },
        equipment: [{
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Equipment'
        }],
        section: [
            {
                title: String,
                exercises: [{
                    exercise: {
                        type: mongoose.Schema.Types.ObjectId,
                        ref: 'Exercise'
                    },
                    type: {
                        type: String,
                        enum: ['rest', 'exercise']
                    },
                    duration: {
                        type: Number
                    }

                }],
            }
        ],
        favouriteCount: {
            type: Number,
            default: 0,
        },
        favouriteBy:[{
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        }],
    },
    { timestamps: true }
);

workoutPlanSchema.plugin(paginate);

const WorkoutPlan = mongoose.model('WorkoutPlan', workoutPlanSchema);
module.exports = {
    WorkoutPlan
};
