const express = require('express');
const router = express.Router();

const coinController = require('../../../controllers/admin/coin.controller');
const { adminProtect } = require('../../../middlewares/adminAuth');

router.get(
    '/latest-transactions',
    adminProtect,
    coinController.getLatestTransaction
);

router.get(
    '/holdings',
    adminProtect,
    coinController.getHoldings
);

router.get(
    '/getPrice',
    adminProtect,
    coinController.getCoinPrice
);

router.get(
    '/stats',
    adminProtect,
    coinController.stats
);

router.get(
    '/coinUtilization',
    adminProtect,
    coinController.coinUtilization
);

router.get(
    '/coinHistory',
    adminProtect,
    coinController.coinHistory
);
module.exports = router;




