{"name": "Ai Top Fit", "version": "1.0.0", "description": "Ai Top Fit-backend", "main": "index.js", "scripts": {"dev": "nodemon src/index.js", "start": "node src/index.js", "test": "echo \"Error: no test specified\" && exit 1", "format": "prettier --write \"src/**/*.js\""}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.350.0", "@aws-sdk/s3-request-presigner": "^3.350.0", "agenda": "^5.0.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "compression": "^1.7.4", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.1.4", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "firebase-admin": "^11.9.0", "helmet": "^7.0.0", "http-status": "^1.6.2", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "mongoose": "^7.2.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemon": "^3.0.1", "openai": "^5.5.1", "referral-code-generator": "^1.0.8", "socket.io": "^4.8.1", "stripe": "^14.21.0", "twilio": "^4.11.2", "tz-offset": "^0.0.2", "uuid": "^9.0.0", "validator": "^13.9.0", "winston": "^3.9.0", "xlsx": "^0.18.5"}, "devDependencies": {"prettier": "^1.19.1", "prettier-airbnb-config": "^1.0.0"}}