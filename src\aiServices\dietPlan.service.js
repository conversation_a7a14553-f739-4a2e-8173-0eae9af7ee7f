const openai = require("../config/openAi");
const { calculateAge } = require("../utils/calculateAge");

async function generateDietPlan(userData, mealList, duration = 30, prompt) {
  console.log(mealList);
  try {
    const {
      name,
      dob,
      gender,
      height,
      weight,
      weightUnit,
      fitnessGoals,
      medicalConditions,
      specificFitnesstargets,
      allergies,
      dietaryRestrictions,
      groceryBudget,
      mealType,
      dietPreference,
      motivation,
    } = userData;
    const age = calculateAge(dob);

    // Format user details
    const userInfo = `
    User Details:
    - Name: ${name || "Not specified"}
    - Age: ${age || "Not specified"}
    - Gender: ${gender || "Not specified"}
    - Height: ${height ? `${height} cm` : "Not specified"}
    - Weight: ${weight ? `${weight} ${weightUnit || "KG"}` : "Not specified"}
    - Fitness Goals: ${
      fitnessGoals.length > 0 ? fitnessGoals.join(", ") : "Not specified"
    }
    - Motivation : ${motivation}
    - Medical Conditions: ${
      medicalConditions.length > 0 ? medicalConditions.join(", ") : "None"
    }
    - Meal Type : ${mealType.length > 0 ? mealType.join(", ") : "Not specified"}
    - Diet Preference : ${dietPreference ? dietPreference : "Not specified"}   
    - Specific Fitness Targets: ${
      specificFitnesstargets.length > 0
        ? specificFitnesstargets.join(", ")
        : "Not specified"
    }
    - Allergies: ${allergies.length > 0 ? allergies.join(", ") : "None"}
    - Dietary Restrictions: ${
      dietaryRestrictions.length > 0 ? dietaryRestrictions.join(", ") : "None"
    }
    - Grocery Budget: ${
      groceryBudget ? `$${groceryBudget} (weekly)` : "Not specified"
    }
    `;

    // Format the meals for the system prompt
    const mealSummary = mealList
      .map((meal) => {
        const ingredientNames = Array.isArray(meal.ingredients)
          ? meal.ingredients.map((ing) => ing.name).join(", ")
          : "No ingredients";

        return `_id: ${meal._id}, ${meal.name}: ${meal.description}, ingredients: [${ingredientNames}] ,`;
      })
      .join("\n");
    console.log(mealSummary);
    let systemPrompt = `
      Create a personalized ${duration}-day diet plan for the user based on the following user details:
      ${userInfo}

      Here are the available meals:
      ${mealSummary}

      Here is the user query:
      ${prompt}
      
      Instructions:
      - Start the response with a 'description' field containing a one-line purpose of the diet plan.
      - Generate a diet plan with unique meals for Breakfast, Lunch, and Dinner.
      - Return only the following for each meal:
        - _id
        - servingSize (in grams)
      - Only pick ids exclusively from the available meals dont add or give a random self generated id   
      - Analyse the user need and the ingredients of each meal , make the diet plan tailor made , keep the Allergies and Dietary Restrictions and special requests in the query a top priority
      - Provide a full response for all ${duration} days.
      - Each day's structure must strictly follow this format:

      {
        "description": "<A one-line purpose of the diet plan>",
        "dietPlan": {
          "Day 1": {
            "Breakfast": { "_id": "", "servingSize": <metion quantity and units , eg - 200gm , 2 pieces> },
            "Lunch": { "_id": "", "servingSize": <metion quantity and units , eg - 200gm , 2 pieces , do not mention dish name>" },
            "Dinner": { "_id": "", "servingSize": <metion quantity and units , eg - 200gm , 2 pieces , do not mention dish name> }
          },
          
          "Day ${duration}": {
            "Breakfast": { "_id": "", "servingSize": <metion quantity and units , eg - 200gm , 2 pieces , do not mention dish name> },
            "Lunch": { "_id": "", "servingSize": <metion quantity and units , eg - 200gm , 2 pieces , do not mention dish name> },
            "Dinner": { "_id": "", "servingSize": <metion quantity and units , eg - 200gm , 2 pieces , do not mention dish name> }
          }
        }
      }
        #IMPORTANT:
        Dont skip a single day , provide full response for all ${duration} days
        Dont give example response but adding ... or continue like this ...
        Only pick ids exclusively from the provided available meals
        You can only pick you cannot generate unique ids , these are linked to db , and adding randomness creates discrepancies  , 
    `;

    // Call OpenAI API for structured diet plan
    const response = await openai.chat.completions.create({
      model: "gpt-4.1",
      messages: [{ role: "system", content: systemPrompt }],
      stream: false,
      temperature: 0.3,
    });

    const responseText = response.choices[0].message.content;
    const jsonMatch = responseText.match(/\{.*\}/s); // Using 's' flag to handle multiline JSON
    if (!jsonMatch) {
      throw new Error("No valid JSON found in the response.");
    }

    console.log(responseText);
    // Parse the response
    const dietPlanResponse = JSON.parse(jsonMatch[0]);

    // Prepare the final output
    const description =
      dietPlanResponse.description || "No description provided";
    const dietPlan = [];

    for (const day in dietPlanResponse.dietPlan) {
      dietPlan.push({
        day: parseInt(day.replace("Day ", ""), 10),
        breakfast: {
          _id: dietPlanResponse.dietPlan[day].Breakfast._id,
          servingSize: dietPlanResponse.dietPlan[day].Breakfast.servingSize,
        },
        lunch: {
          _id: dietPlanResponse.dietPlan[day].Lunch._id,
          servingSize: dietPlanResponse.dietPlan[day].Lunch.servingSize,
        },
        dinner: {
          _id: dietPlanResponse.dietPlan[day].Dinner._id,
          servingSize: dietPlanResponse.dietPlan[day].Dinner.servingSize,
        },
      });
    }

    return { description, dietPlan };
  } catch (error) {
    console.error("Error generating diet plan:", error);
    throw new Error("Failed to generate diet plan.");
  }
}

module.exports = { generateDietPlan };
