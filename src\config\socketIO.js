const { Server } = require("socket.io");

let io; // Declare `io` outside the function for global access

function initializeSocket(server) {
  io = new Server(server, {
    cors: {
      origin: "*", // Allow all origins
      methods: ["GET", "POST"],
    },
  });

  io.on("connection", (socket) => {
    console.log(`User connected with socket ID: ${socket.id}`);

    socket.on("join", (userId) => {
      socket.join(userId);
      console.log(`User with ID ${userId} joined room`);
    });

    socket.on("disconnect", (reason) => {
      console.log(`User disconnected. Reason: ${reason}`);
    });
  });

  return io;
}

function getSocketInstance() {
  if (!io) {
    throw new Error(
      "Socket.io is not initialized. Call `initializeSocket` first."
    );
  }
  return io;
}

module.exports = { initializeSocket, getSocketInstance };
