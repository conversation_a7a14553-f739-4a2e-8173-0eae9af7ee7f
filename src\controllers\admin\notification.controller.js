const httpStatus = require("http-status");
const catchAsync = require("../../utils/catchAsync");
const { Notification } = require("../../models/notification.model");
const notiFunc = require('../../microservices/notification.service');
const { getPaginateConfig } = require("../../utils/queryPHandler");
const { User } = require("../../models");
const UserNotification = require("../../models/userNotification.model");
const agenda = require("../../config/agenda");


const list = catchAsync(async (req, res) => {
    const {filters,options} = getPaginateConfig(req.query);
 
    if(filters.search){
        filters.$or = [
            {title:{$regex:filters.search,$options:"i"}},
            {description:{$regex:filters.search,$options:"i"}}
        ];
        delete filters.search;
    }

    if(filters.timeFrame){

        let start;
        let end;
        const today = new Date();
        if(filters.timeFrame == "today"){
            start = new Date();
            start.setHours(0, 0, 0, 0);

            end = new Date();
            end.setHours(23, 59, 59, 999);
        }else if(filters.timeFrame == "thisMonth"){
            start = new Date(today.getFullYear(), today.getMonth(), 1);
            start.setHours(0, 0, 0, 0);
            
            end = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            end.setHours(23, 59, 59, 999);
        }else if(filters.timeFrame == "thisWeek"){
            start = new Date(today.setDate(today.getDate() - today.getDay()));
            start.setHours(0, 0, 0, 0);

            end = new Date(start);
            end.setDate(end.getDate() + 6);
            end.setHours(23, 59, 59, 999);
        }else if(filters.timeFrame == "thisYear"){
            start = new Date(today.getFullYear(), 0, 1);
            start.setHours(0, 0, 0, 0);
            
            end = new Date(today.getFullYear(), 11, 31);
            end.setHours(23, 59, 59, 999);
        }

        if(filters.timeFrame != "all"){
            filters.createdAt = { $gte: start, $lte: end }
        }

        delete filters.timeFrame;
    }

    const notifications = await Notification.paginate(filters,options);
    res.status(200).json({ status: true, data: notifications })
});

const detail = catchAsync(async (req, res) => {
    const {id} = req.params;
    const notification = await Notification.findById(id);
    res.status(200).json({ status: true, data: notification })
});

const sendNotification = catchAsync(async (req, res) => {

    // const user = req.user;
    const body = req.body;

    const topic = body.reciever ?? "all";

    let GmtScheduledAt = new Date();

    if(body.isScheduled){
        var timeZoneOffsetInMs = body.TimezoneOffset * 60 * 1000;
        const scheduledAt = new Date(body.scheduledAt);
        console.log("scheduledAt",scheduledAt);
        GmtScheduledAt = new Date(scheduledAt.getTime() + timeZoneOffsetInMs);
        console.log("GmtScheduledAt",GmtScheduledAt);    
    }

    const notification = await Notification.create({
        title:body.title,
        description:body.description,
        isForAll: 0,
        isScheduled:body.isScheduled,
        scheduledAt:GmtScheduledAt,
        isDelivered:!body.isScheduled
    })

    if (!body.isScheduled) {

        const users = await User.find({isBlocked:false,isDeleted:{$ne:true}}).select("_id");

        const userNotiData = [];

        users.forEach(user => {
            userNotiData.push({
                receiver:user._id,
                notification:notification._id,
                title:notification.title,
                description:notification.description,
            })
        });

        const userNotifications = await UserNotification.create(userNotiData);

        await notiFunc.sendToTopic(topic, { title: body.title, body: body.description });
    }else{
        agenda.schedule(notification.scheduledAt, "scheduleNotification", {notiId:notification._id}); 
    }

    

    return res.status(200).json({ status: true, notification })
});


const deleteNotification = catchAsync(async (req, res) => {
    await Notification.findByIdAndDelete(req.params.id);
    return res.status(200).json({ status: true, msg: "Notification Deleted Succesfully" });
})


module.exports = {
    list,
    sendNotification,
    deleteNotification,
    detail
};