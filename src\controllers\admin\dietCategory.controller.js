const catchAsync = require("../../utils/catchAsync");
const dietCategoryService = require("../../services/dietCategory.service");
const { getPaginateConfig } = require("../../utils/queryPHandler");

const getDietCategories= catchAsync(async (req, res, next) => {
    try {
        const { filters,options } = getPaginateConfig(req.query);

        if(filters.search){
            filters.$or = [
                {name:{$regex:filters.search,$options:"i"}},
            ];
            delete filters.search;
        }
    
        if(filters.timeFrame){
    
            let start;
            let end;
            const today = new Date();
            if(filters.timeFrame == "today"){
                start = new Date();
                start.setHours(0, 0, 0, 0);
    
                end = new Date();
                end.setHours(23, 59, 59, 999);
            }else if(filters.timeFrame == "thisMonth"){
                start = new Date(today.getFullYear(), today.getMonth(), 1);
                start.setHours(0, 0, 0, 0);
                
                end = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                end.setHours(23, 59, 59, 999);
            }else if(filters.timeFrame == "thisWeek"){
                start = new Date(today.setDate(today.getDate() - today.getDay()));
                start.setHours(0, 0, 0, 0);
    
                end = new Date(start);
                end.setDate(end.getDate() + 6);
                end.setHours(23, 59, 59, 999);
            }else if(filters.timeFrame == "thisYear"){
                start = new Date(today.getFullYear(), 0, 1);
                start.setHours(0, 0, 0, 0);
                
                end = new Date(today.getFullYear(), 11, 31);
                end.setHours(23, 59, 59, 999);
            }
    
            if(filters.timeFrame != "all"){
                filters.createdAt = { $gte: start, $lte: end }
            }
    
            delete filters.timeFrame;
        }

        const dietCategories= await dietCategoryService.getDietCategorys(filters,options);
        res.status(200).send({ data: dietCategories, message: '' });
    } catch (error) {
        next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing dietCategorys', error));
    }
});

const getDietCategory = catchAsync(async (req, res, next) => {

    const dietCategory = await dietCategoryService.getDietCategoryById(req.params.id);
    res.status(200).send({ data: dietCategory, message: '' });

});

const store = catchAsync(async (req, res, next) => {

    const dietCategory = await dietCategoryService.createDietCategory(req.body, req.file);
    res.status(201).send({ data: dietCategory, message: 'DietCategory is created Successfully' });

});

const updateDietCategory = catchAsync(async (req, res, next) => {

    const dietCategory = await dietCategoryService.updateDietCategoryById(req.body._id, req.body, req.file);
    res.status(200).send({ data: dietCategory, message: 'DietCategory is updated Successfully' });

});

const deleteDietCategory = catchAsync(async (req, res, next) => {

    const dietCategory = await dietCategoryService.deleteDietCategoryById(req.params.id);
    res.status(200).send({ data: dietCategory, message: 'DietCategory is deleted Successfully' });

});

module.exports = {
    store,
    getDietCategory,
    getDietCategories,
    updateDietCategory,
    deleteDietCategory
}