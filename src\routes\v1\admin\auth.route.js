const express = require('express');
const router = express.Router();

const validate = require('../../../middlewares/validate');
const adminAuthValidation = require('../../../validations/adminAuth.validation');

const { adminProtect } = require('../../../middlewares/adminAuth');


const authController = require('../../../controllers/admin/auth.controller');


router.post(
  '/login',
  authController.loginAdmin
);

router.post(
  '/updatePassword',
  adminProtect,
  authController.updatePassword
);

router.post(
  '/getDetails',
  adminProtect,
  authController.getDetails
);

module.exports = router;
