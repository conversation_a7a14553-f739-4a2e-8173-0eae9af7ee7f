const express = require('express');
const router = express.Router();

const { challengeController,challengeParticipateController } = require('../../controllers');
const { firebaseAuth } = require('../../middlewares/firebaseAuth');
const { fileUploadService } = require('../../microservices');

router.get('/list/:type', firebaseAuth, challengeController.getAllChallenges);
router.get('/:id', firebaseAuth, challengeController.getChallengeById);

router.post('/participate',
    fileUploadService.multerUpload.single('video'),
    firebaseAuth,
    challengeParticipateController.participate
);

router.get('/leaderboard/:id',firebaseAuth,  challengeParticipateController.leaderboard);


module.exports = router;
