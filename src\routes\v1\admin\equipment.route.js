const express = require('express');
const router = express.Router();

const validate = require('../../../middlewares/validate');
const { adminProtect } = require('../../../middlewares/adminAuth');
const equipmentValidation = require('../../../validations/equipment.validation');
const searchValidation = require('../../../validations/search.validation');

const equipmentController = require('../../../controllers/admin/equipment.controller');
const { fileUploadService } = require('../../../microservices');


router.post(
    '/add',
    adminProtect,
    fileUploadService.multerUpload.single('thumbnail'),
    validate(equipmentValidation.AddEquipment),
    equipmentController.store
);

router.post(
    '/update',
    adminProtect,
    fileUploadService.multerUpload.single('thumbnail'),
    validate(equipmentValidation.EditEquipment),
    equipmentController.updateEquipment
)

router.get(
    '/list',
    adminProtect,
    validate(searchValidation.query),
    equipmentController.getEquipments
);
router.get(
    '/:id',
    adminProtect,
    equipmentController.getEquipment
);

router.delete(
    '/:id',
    adminProtect,
    equipmentController.deleteEquipment
);

module.exports = router;
