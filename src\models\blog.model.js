const mongoose = require('mongoose');
const { paginate } = require('./plugins/paginate');

const blogSchema = new mongoose.Schema(
    {
        thumbnail: {
            type: {
                key: String,
                url: String,
            },
            default: null,
        },
        title: {
            type: String,
            default: null,
        },
        description: {
            type: String,
            default: null,
        },
        body: {
            type: String,
            default: null,
        },
        tags: [{
            type: String
        }],

    },
    { timestamps: true }
)

blogSchema.plugin(paginate);

const Blog = mongoose.model('Blog', blogSchema);
module.exports = {
    Blog
};