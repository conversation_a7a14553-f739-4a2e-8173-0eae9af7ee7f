const express = require('express');
const router = express.Router();

const { dietPlanController } = require('../../controllers');
const { firebaseAuth } = require('../../middlewares/firebaseAuth');


router.get('/dietLogs', firebaseAuth,dietPlanController.getDietLog);
router.get('/categories', firebaseAuth,dietPlanController.getDietCategories);


router.get('/', firebaseAuth, dietPlanController.getAlldietPlan);

router.get('/:id', dietPlanController.getdietPlanById);

router.post('/addLog',firebaseAuth, dietPlanController.addDietLog);
router.post('/addCustomLog', firebaseAuth,dietPlanController.addCustomDietLog);



module.exports = router;
