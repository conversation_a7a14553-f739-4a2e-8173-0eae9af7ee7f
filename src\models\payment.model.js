const {default: mongoose} = require('mongoose');
const {paginate} = require('./plugins/paginate');
const { number } = require('joi');

const paymentSchema = new mongoose.Schema(
  {
    sessionId: {
      type: String,
    },
    invoiceId:{
      type: String,
    },
    planName:{
      type:String,
      default:null
    },
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    coins: {
      type: Number,
      default: 0 
    },
    payment_intent: {
      type: String,
      default: null,
    },
    status: {
      type: String,
      default: null,
    },
    payment_status: {
      type: String,
      default: null, // should be paid if transaction is successful
    },
    product:{
      type:String,
      default:"coin"
    },
    amount:{
        type:Number,
        default:0
    },
    mode:{
        type:String,
        enum:["subscription","payment"]
    },
    invoiceUrl:{
        type:String
    },
    metadata:{
        type:Object,
        default:{}
    }
  },
  {timestamps: true}
);

paymentSchema.plugin(paginate);
paymentSchema.statics.getSalesAndRevenueByMonth = async function(filters, year) {
  const results = await this.aggregate([
    {
      $match: {
        ...filters,
        payment_status: 'paid',
        createdAt: {
          $gte: new Date(`${year}-01-01T00:00:00.000Z`),
          $lt: new Date(`${year + 1}-01-01T00:00:00.000Z`),
        },
      },
    },
    {
      $group: {
        _id: {
          $dateToString: {format: '%Y-%m', date: '$createdAt'},
        },
        count: {$sum: 1},
        totalRevenue: {$sum: '$amount'},
      },
    },
    {
      $sort: {_id: 1},
    },
  ]);
  // Fill in missing months with zero completions
  const resultWithZeros = [];
  for (let i = 1; i <= 12; i++) {
    const date = new Date(year+"-"+i);
    resultWithZeros.push({month:date.toLocaleString('en-us', { month: 'short' }), count: 0, totalRevenue: 0});
  }
  (results || []).forEach(({_id, count, totalRevenue}) => {
    const month = Number(_id.split('-')[1]);
    const date = new Date(_id)
    resultWithZeros[month - 1] = {month:date.toLocaleString('en-us', { month: 'short' }), count, totalRevenue};
  });
  return resultWithZeros;
};

paymentSchema.statics.getSalesAndRevenueByWeekDays = async function(filters, week) {
  const today = new Date();
  let WeekStartDate;
  let WeekEndDate;
  const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  if(week == "last"){
    WeekStartDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay() - 6);
    WeekEndDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay());
  }else{
    WeekStartDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay());
    WeekEndDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() + (6 - today.getDay()));
  }

 
  const results = await this.aggregate([
    {
      $match: {
        ...filters,
        payment_status: 'paid',
        createdAt: {
          $gte: WeekStartDate,
          $lt: WeekEndDate,
        },
      },
    },
    {
      $group: {
        _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
        count: { $sum: 1 },
        totalRevenue: { $sum: '$amount' },
      },
    },
    {
      $sort: { _id: 1 },
    },
  ]);

  console.log(results)
  // Fill in missing months with zero completions
  const resultWithZeros = [];
  for (let i = 1; i <= 7; i++) {
    resultWithZeros.push({day: daysOfWeek[i-1], count: 0, totalRevenue: 0});
  }
  (results || []).forEach(({_id, count, totalRevenue}) => {
    const date = new Date(_id);
    const day = date.getDay();
    resultWithZeros[day] = {day:daysOfWeek[day], count, totalRevenue};
  });
  return resultWithZeros;
};

paymentSchema.statics.getSalesAndRevenueByDayofMonth = async function(filters, year) {
  const today = new Date();
  const month  = (today.getMonth() + 1).length > 1  ? (today.getMonth() + 1): "0"+(today.getMonth() + 1);
  const month2  = (today.getMonth() + 2).length > 1  ? (today.getMonth() + 2): "0"+(today.getMonth() + 2);

  console.log("start",new Date(`${year}-${month}-01T00:00:00.000Z`))
  console.log("end",new Date(`${year}-${month2}-01T00:00:00.000Z`));
  const results = await this.aggregate([
    {
      $match: {
        ...filters,
        payment_status: 'paid',
        createdAt: {
          $gte: new Date(`${year}-${month}-01T00:00:00.000Z`),
          $lt: new Date(`${year}-${month2}-01T00:00:00.000Z`),
        },
      },
    },
    {
      $group: {
        _id: {
          $dateToString: {format: '%Y-%m-%d', date: '$createdAt'},
        },
        count: {$sum: 1},
        totalRevenue: {$sum: '$amount'},
      },
    },
    {
      $sort: {_id: 1},
    },
  ]);
  // Fill in missing months with zero completions
  const resultWithZeros = [];

  let lastDate = 30
  if([1,3,5,7,8,10,12].includes(Number(month))){
    lastDate = 31;
  }else if(Number(month) == 2){
      lastDate = 28;
  }

  for (let i = 1; i <= lastDate; i++) {
    const date = new Date(year,Number(month)-1,i);
    const day = date.getDay();
    const dateString = date.getDate() +"-"+ date.toLocaleString('en-us', { month: 'short' });
    resultWithZeros.push({day: dateString, count: 0, totalRevenue: 0});
  }
  (results || []).forEach(({_id, count, totalRevenue}) => {
    const date = new Date(_id);
    const day = date.getDate();
    const dateString = date.getDate() +"-"+ date.toLocaleString('en-us', { month: 'short' });

    resultWithZeros[day-1] = {day:dateString, count, totalRevenue};
  });
  return resultWithZeros;
};

module.exports.Payment = mongoose.model('Payment', paymentSchema);