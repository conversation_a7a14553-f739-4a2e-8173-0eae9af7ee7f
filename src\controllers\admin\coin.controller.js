const catchAsync = require("../../utils/catchAsync");
const coinOfferService = require("../../services/coinOffer.service");
const ApiError = require("../../utils/ApiError");
const httpStatus = require("http-status");
const { CoinWalletTransaction } = require("../../models/coinWalletTransaction.model");
const { userCoinWalletService } = require("../../services");
const { getPaginateConfig } = require("../../utils/queryPHandler");
const { Setting } = require("../../models/setting.model");

const getLatestTransaction = catchAsync(async (req, res, next) => {

    const transactions = await CoinWalletTransaction.find({}).populate("userId","_id name profilePic").sort({createdAt:-1}).limit(3);
    res.status(200).send({status:true, data: transactions});
});

const getHoldings = catchAsync(async (req, res, next) => {

    const {filters,options} = getPaginateConfig(req.query);

    options.populate = [
        "userId::_id,name,profilePic"
    ]; 
    const wallets = await userCoinWalletService.getUsersWallets(filters,options);
    res.status(200).send({status:true, data: wallets});
});


const stats = catchAsync(async (req, res, next) => {
    const {filters,options} = getPaginateConfig(req.query);

    const totalHoldings = await userCoinWalletService.getTotalHoldings(filters,options);
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    filters.createdAt =  { $gte: sevenDaysAgo }
    const newPurchase = await userCoinWalletService.getTotalPurchase(filters,options);
    const totalSpend = await userCoinWalletService.getTotalSpend(filters,options);
    const totalRevenue = await userCoinWalletService.getRevenue(filters,options);

    res.status(200).send({status:true, data:{totalHoldings,totalSpend,totalRevenue,newPurchase}});
});

const coinUtilization = catchAsync(async (req, res, next) => {

    const {week} = req.query;
    const coinWalletTransaction = await CoinWalletTransaction.getSalesAndRevenueByWeekDays({transactionType: 'debit',},week);
    res.status(200).send({status:true, data: coinWalletTransaction});
});


const coinHistory = catchAsync(async (req, res, next) => {

    const {week} = req.query;
    const supplied = await CoinWalletTransaction.getSalesAndRevenueByWeekDays({transactionType: 'credit',},week);
    const distributed = await CoinWalletTransaction.getSalesAndRevenueByWeekDays({transactionType: 'debit',},week);

    res.status(200).send({status:true, data: {supplied,distributed}});
});

const getCoinPrice = catchAsync(async (req, res, next) => {
    const coinPrice = await Setting.findOne({});
    res.status(200).send({status:true, data: coinPrice});
})



module.exports = {
    getLatestTransaction,
    getHoldings,
    getCoinPrice,
    stats,
    coinUtilization,
    coinHistory
}