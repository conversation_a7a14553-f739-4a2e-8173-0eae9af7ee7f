const mongoose = require('mongoose');
const { paginate } = require('./plugins/paginate');
const { number } = require('joi');

const challengeSchema = new mongoose.Schema(
    {
        thumbnail: {
            type: {
                key: String,
                url: String,
            },
            default: null,
        },
        title: {
            type: String,
            default: null,
        },
        description: {
            type: String,
            default: null,
        },
        duration: {
            type: Number,
            default: null,
        },
        calories: {
            type: Number,
            default: null,
        },
        type: {
            type: String,
            enum: ["Beginner", "Normal", "Medium", "Advanced"],
            default: null
        },
        entryCoins: {
            type: Number,
            default: null,
        },
        reEntryCoins: {
            type: Number,
            default: null,
        },
        prizeCoins: {
            firstPrize:{
                type:Number,
            },
            secondPrize:{
                type:Number,
            },
            thirdPrize:{
                type:Number,
            }

        },
        rules: {
            type: String,
            default: null,
        },
        howToDoIt: {
            type: String,
            default: null,
        },
        refVideo: {
            type: {
                key: String,
                url: String,
            },
            default: null,
        },
        refVideoTitle: {
            type: String,
            default: null,
        },
        status: {
            type: String,
            enum: ["Active", "Ended"],
            default: "Active"
        },
        startDate: {
            type: Date,
            default: null
        },
        endDate: {
            type: Date,
            default: null
        },
        priceDistributed:{
            type:Boolean,
            default:false
        },
        IsEnded:{
            type:Boolean,
            default:false
        }
    },
    { timestamps: true }
)

challengeSchema.plugin(paginate);

const Challenge = mongoose.model('Challenge', challengeSchema);
module.exports = {
    Challenge
};