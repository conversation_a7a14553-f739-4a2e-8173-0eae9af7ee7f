const { User, Client } = require('../models');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { CoinOffer } = require('../models/coinOffer.model');


async function getCoinOffers() {
    return await CoinOffer.find();
}

async function getUserCoinOffers() {
    return await CoinOffer.find({ isActive: true });
}

async function getCoinOfferById(id) {
    return await CoinOffer.findById(id);
}

async function createCoinOffer(details) {
    return await CoinOffer.create(details);
}

async function updateCoinOfferById(id, newDetails) {
    const coinOffer = await CoinOffer.findById(id);
    let updates;

    if (!coinOffer) {
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'No offers found');
    }

    updates = { ...newDetails };
    return await CoinOffer.findByIdAndUpdate(id, updates, { new: true });
}

async function deleteCoinOfferById(id) {
    try {
        await CoinOffer.findByIdAndDelete(id);
        return true;
    } catch (err) {
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to delete the offer');
    }
}


module.exports = {
    getCoinOffers,
    getCoinOfferById,
    createCoinOffer,
    updateCoinOfferById,
    deleteCoinOfferById,
    getUserCoinOffers
};