const { User, Client } = require('../models');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { CoinOffer } = require('../models/coinOffer.model');
const { DietLog } = require('../models/dietLog.model');


async function addDietLog(details) {

    return await DietLog.create(details);
}

async function getUserdietLog(userId) {
    return await DietLog.find({ userId }).populate("recipeId").sort({createdAt:-1});
}


module.exports = {
    addDietLog,
    getUserdietLog
};