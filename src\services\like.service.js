const { User, Client } = require('../models');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { Like } = require('../models/like.model');


async function getLikes(filter) {
    return await Like.find(filter).sort({ createdAt: -1 });
}

async function getLike(data) {
    return await Like.findOne(data);
}

async function createLike(details) {
    return await Like.create(details);
}

async function deleteLikeById(data) {

    try {
        await Like.deleteOne(data);
        return true;
    } catch (err) {
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to delete the Like');
    }
}


module.exports = {
    getLikes,
    getLike,
    createLike,
    deleteLikeById
};