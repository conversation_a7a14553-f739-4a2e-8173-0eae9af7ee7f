const express = require('express');
const router = express.Router();
const { firebaseAuth } = require('../../middlewares/firebaseAuth');
// const validate = require('../../../middlewares/validate');
// const { adminProtect } = require('../../../middlewares/adminAuth');
// const equipmentValidation = require('../../../validations/equipment.validation');

const postController = require('../../controllers/post.controller');
const likeController = require('../../controllers/like.controller');
const { fileUploadService } = require('../../microservices');


router.post(
    '/add',
    fileUploadService.multerUpload.single('image'),
    firebaseAuth,
    // validate(equipmentValidation.AddEquipment),
    postController.store
);

router.post(
    '/update',
    fileUploadService.multerUpload.single('image'),
    firebaseAuth,
    // validate(equipmentValidation.EditEquipment),
    postController.updatePost
)

router.get(
    '/list/:type',
    firebaseAuth,
    postController.getPosts
);
router.get(
    '/:id',
    firebaseAuth,
    postController.getPost
);

router.delete(
    '/:id',
    firebaseAuth,
    postController.deletePost
);

router.post(
    '/like',
    firebaseAuth,
    likeController.like
);

router.get(
    '/like/list/:postId',
    firebaseAuth,
    likeController.getlikes
);

module.exports = router;
