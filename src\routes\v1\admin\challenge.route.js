const express = require('express');
const router = express.Router();

const validate = require('../../../middlewares/validate');
const { adminProtect } = require('../../../middlewares/adminAuth');
const challengeValidation = require('../../../validations/challenge.validation');
const searchValidation = require('../../../validations/search.validation');

const challengeController = require('../../../controllers/admin/challenge.controller');

const { fileUploadService } = require('../../../microservices');


router.post(
    '/add',
    adminProtect,
    fileUploadService.multerUpload.fields([
        { name: 'refVideo', maxCount: 1 },
        { name: 'thumbnail', maxCount: 1 }
    ]),
    validate(challengeValidation.AddChallenge),
    challengeController.store
);

router.post(
    '/update',
    adminProtect,
    fileUploadService.multerUpload.fields([
        { name: 'refVideo', maxCount: 1 },
        { name: 'thumbnail', maxCount: 1 }
    ]),
    validate(challengeValidation.EditChallenge),
    challengeController.updateChallenge
);

router.get(
    '/list',
    adminProtect,
    validate(searchValidation.query),
    challengeController.getChallenges
);

router.get(
    '/leaderboard/:id',
    adminProtect,
    challengeController.leaderboard
);

router.get(
    '/participants/:id',
    adminProtect,
    challengeController.getChallengeParticates
);

router.post(
    '/update-participants',
    adminProtect,
    challengeController.updateChallengeParticates
);

router.post(
    '/markAsEnded',
    adminProtect,
    challengeController.markAsEnded
);
router.get(
    '/:id',
    adminProtect,
    challengeController.getChallenge
);

router.delete(
    '/:id',
    adminProtect,
    challengeController.deleteChallenge
);

module.exports = router;