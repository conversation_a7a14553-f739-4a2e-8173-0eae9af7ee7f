const httpStatus = require("http-status");
const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");
const { workoutPlanService } = require("../services");
const { default: mongoose } = require("mongoose");
const { CustomWorkoutPlan } = require("../models/customWorkoutModel");
const moment = require("moment");
const { WorkoutPlan } = require("../models/workoutPlan.model");

const getAllWorkoutPlan = catchAsync(async (req, res) => {
  const user = req.user;

  if (req.query.search) {
    req.query.$or = [
      { name: { $regex: req.query.search, $options: "i" } }, // Case-insensitive match for 'name'
      { description: { $regex: req.query.search, $options: "i" } }, // Case-insensitive match for 'description'
      // Add more fields as needed for the keyword search
    ];

    delete req.query.search;
  } else {
    if (!req.query.type && !req.query.equipment) {
      req.query.idealFor = user.activityLevel;
    }
  }

  if (req.query.equipment) {
    req.query.equipment = new mongoose.Types.ObjectId(req.query.equipment);
  }

  console.log(req.query);

  const workoutPlans = await workoutPlanService.getAllWorkoutPlanWithCustomFilters(
    req.query,
    {}
  );
  res.status(200).json({
    message: "All workout plan",
    data: workoutPlans,
  });
});

const getWorkoutPlanById = catchAsync(async (req, res) => {
  const workoutPlan = await workoutPlanService.getWorkoutPlanById(
    req.params.id
  );
  res.status(200).json({
    message: "Work out plan details",
    data: workoutPlan,
  });
});

const startWorkout = catchAsync(async (req, res, next) => {
  console.log("📌 startWorkout API called");

  const { id } = req.user;
  const { custom, workoutPlanId } = req.body;

  console.log(`➡️ User ID: ${id}`);
  console.log(`➡️ Custom Plan: ${custom}`);
  console.log(`➡️ Workout Plan ID: ${workoutPlanId}`);

  const startTime = new Date();
  console.log(`🕒 Start Time: ${startTime}`);

  const planType = custom ? "custom" : "default"; // Determine the plan type
  console.log(`📌 Plan Type: ${planType}`);

  let exercises = [];

  if (custom) {
    console.log("🔍 Fetching Custom Workout Plan...");
    const customPlan = await CustomWorkoutPlan.findById(workoutPlanId);
    if (!customPlan) {
      console.error("❌ Custom workout plan not found");
      return res
        .status(httpStatus.NOT_FOUND)
        .send({ message: "Custom workout plan not found." });
    }

    console.log("✅ Custom Plan Found. Fetching Exercises for Today...");

    const todayDate = moment().format("YYYY-MM-DD");
    console.log(`📅 Today's Date: ${todayDate}`);

    const todayWorkout = customPlan.workoutPlan.find(
      (day) => day.date === todayDate
    );

    if (todayWorkout) {
      console.log("📌 Found Today's Custom Workout");
      exercises = todayWorkout.exercises.map((exercise) => ({
        exerciseId: exercise._id,
        sets: exercise.sets,
        reps: exercise.reps,
        weight: exercise.weight || null,
        caloriesBurnt: exercise.caloriesBurnt || null,
      }));
    } else {
      console.warn("⚠️ No Workout Scheduled for Today in Custom Plan.");
    }
  } else {
    console.log("🔍 Fetching Default Workout Plan...");
    const defaultPlan = await WorkoutPlan.findById(workoutPlanId);
    if (!defaultPlan) {
      console.error("❌ Default workout plan not found");
      return res
        .status(httpStatus.NOT_FOUND)
        .send({ message: "Default workout plan not found." });
    }

    console.log("✅ Default Plan Found. Fetching Exercises...");
    exercises = await workoutPlanService.getAllPlanExcercies(workoutPlanId);
    if (!exercises || exercises.length === 0) {
      console.warn("⚠️ No Exercises Found in Default Plan.");
    } else {
      console.log(`📌 ${exercises.length} Exercises Found.`);
    }

    exercises = exercises.map((exercise) => ({
      exerciseId: exercise._id,
      sets: exercise.sets,
      reps: exercise.reps,
      weight: exercise.weight || null,
      caloriesBurnt: exercise.caloriesBurnt || null,
    }));
  }

  console.log("✅ Creating User Workout Plan...");
  const userWorkout = await workoutPlanService.createUserWorkoutPlan({
    ...req.body,
    userId: id,
    startTime,
    planType,
    workoutPlanId: !custom ? workoutPlanId : null,
    customPlanId: custom ? workoutPlanId : null,
    exercisesPerformed: exercises,
  });

  console.log("🎉 Workout Plan Created Successfully!");
  console.log("📌 Response Data:", {
    data: userWorkout,
    exercises,
  });

  res.status(201).send({
    data: userWorkout,
    exercises,
    message: "Workout Plan started successfully",
  });
});

const endWorkout = catchAsync(async (req, res, next) => {
  console.log("📌 endWorkout API called");

  const { userWorkoutId, duration } = req.body;
  console.log(`➡️ User Workout ID: ${userWorkoutId}`);
  console.log(`➡️ Provided Duration: ${duration || "Not Provided"}`);

  const endTime = new Date();
  console.log(`🕒 End Time: ${endTime}`);

  // ✅ Get the workout plan by ID
  const plan = await workoutPlanService.getUserWorkoutPlanById(userWorkoutId);
  if (!plan) {
    console.error("❌ Invalid Workout Plan ID");
    throw new ApiError(httpStatus.BAD_REQUEST, "Invalid Workout Plan ID");
  }

  console.log("✅ Workout Plan Found. Calculating Calories Burnt...");

  // ✅ Calculate total calories burned based on performed exercises
  let totalCaloriesBurnt = 0;
  if (plan.exercisesPerformed && plan.exercisesPerformed.length > 0) {
    totalCaloriesBurnt = plan.exercisesPerformed.reduce((total, exercise) => {
      return total + (exercise.caloriesBurnt || 0);
    }, 0);
  }

  console.log(`🔥 Total Calories Burnt: ${totalCaloriesBurnt}`);

  // ✅ Calculate duration if not provided
  const calculatedDuration =
    duration || Math.round((endTime - plan.startTime) / (1000 * 60));
  console.log(`⏳ Final Duration: ${calculatedDuration} minutes`);

  // ✅ Update the workout session with end time, duration, and calories
  const workoutPlan = await workoutPlanService.updateUserWorkoutById(
    userWorkoutId,
    {
      duration: calculatedDuration,
      endTime,
      calories: totalCaloriesBurnt,
    }
  );

  console.log("🎉 Workout Plan Updated Successfully!");
  console.log("📌 Response Data:", workoutPlan);

  res.status(201).send({
    data: workoutPlan,
    message: "Workout Plan ended successfully",
  });
});

const workoutHistory = catchAsync(async (req, res, next) => {
  const user = req.user;
  const history = await workoutPlanService.getUserWorkouts(user._id);
  res.status(200).send({ data: history, status: true });
});

const calender = catchAsync(async (req, res, next) => {
  const user = req.user;
  const { year, month } = req.query;

  const currentDate = new Date();
  const currentMonth = month ? month : currentDate.getMonth() + 1; // Months are zero-based, so add 1
  const currentYear = year ? year : currentDate.getFullYear();

  const history = await workoutPlanService.getUserWorkoutsForMonth(
    user._id,
    currentMonth,
    currentYear
  );

  res.status(200).send({ data: history, status: true });
});

const workoutByDate = catchAsync(async (req, res, next) => {
  console.log("📌 workoutByDate API called");

  const user = req.user;
  const { date } = req.query;

  console.log(`➡️ Requested Date: ${date}`);

  if (!date) {
    return res.status(400).send({ status: false, message: "Date is required" });
  }

  const currentDate = moment.utc(date, "YYYY-MM-DD").toDate();
  const currentMonth = currentDate.getUTCMonth() + 1; // Months are zero-based
  const currentYear = currentDate.getUTCFullYear();
  const day = currentDate.getUTCDate();

  console.log(
    `📅 Fetching workouts for: ${day}-${currentMonth}-${currentYear}`
  );

  const history = await workoutPlanService.getUserWorkoutsForDay(
    user._id,
    day,
    currentMonth,
    currentYear
  );

  console.log("✅ Workout Data Retrieved:", history);

  res.status(200).send({ data: history, status: true });
});

module.exports = {
  getAllWorkoutPlan,
  getWorkoutPlanById,
  startWorkout,
  endWorkout,
  workoutHistory,
  calender,
  workoutByDate,
};
