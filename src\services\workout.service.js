const {User, Client} = require('../models');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { fileUploadService } = require('../microservices');
const { Workout } = require('../models/workout.model');


async function getWorkoutById(id) {
  const workout = await Workout.findById(id);
  return workout;
}

async function getWorkouts(filters, options) {
  return await Workout.paginate(filters, options);
}

async function createWorkout(details,workoutVideo) {
    let data = {...details};
    if (workoutVideo) {
      const [video] = await fileUploadService.s3Upload([workoutVideo], 'video').catch(err => {
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload profile picture');
      });
      data = {...data, video};
    }
    return await Workout.create(data);
}

async function updateWorkoutById(id, newDetails,workoutVideo) {
  const workout = await Workout.findById(id);
  let updates = {...newDetails};
  if (workoutVideo) {
    const [video] = await fileUploadService.s3Upload([workoutVideo], 'video').catch(err => {
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload profile picture');
    });
    if (workout.video) {
      const oldPicKey = workout.video.key;
      await fileUploadService
        .s3Delete(oldPicKey)
        .catch(err => console.log('Failed to delete profile picture', oldPicKey));
    }
    updates = {...updates, video};
  }
    return await Workout.findByIdAndUpdate(id, updates, {new: true});
}

async function deleteWorkoutById(id) {
  try {
    await Workout.findByIdAndDelete(id);
    return true;
  } catch (err) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to delete the user');
  }
}


module.exports = {
  getWorkouts,
  getWorkoutById,
  updateWorkoutById,
  deleteWorkoutById,
  createWorkout
};
