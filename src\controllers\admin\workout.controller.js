const catchAsync = require("../../utils/catchAsync");
const workoutService = require("../../services/workout.service");

const store = catchAsync(async (req,res,next) => {

    const workout = await workoutService.createWorkout(req.body,req.file);
    res.status(201).send({data: workout, message: 'Workout is created Successfully'});

});

const updateWorkout = catchAsync(async (req,res,next) => {

    const workout = await workoutService.updateWorkoutById(req.body._id,req.body,req.file);
    res.status(200).send({data: workout, message: 'Workout is update Successfully'});

});

const getWorkouts = catchAsync(async (req,res,next) => {

    const workouts = await workoutService.getWorkouts({category:req.params.cat},{});
    res.status(200).send({data: workouts, message: ''});

});

const getWorkout = catchAsync(async (req,res,next) => {

    const workout = await workoutService.getWorkoutById(req.params.id);
    res.status(200).send({data: workout, message: ''});

});

module.exports = {
    store,
    updateWorkout,
    getWorkouts,
    getWorkout
}
