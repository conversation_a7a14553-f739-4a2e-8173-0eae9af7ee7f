const config = require('../config/config');
const { CoinWalletTransaction } = require('../models/coinWalletTransaction.model');
const { Payment } = require('../models/payment.model');
const {User} = require('../models/user.model');
const {UserCoinWallet} = require('../models/userCoinWallet.model');

const stripe = require('stripe')(config.stripe.secret);

async function createAndGetWallet(userId) {
  
    let coinWallet = await UserCoinWallet.findOne({userId});

    if(!coinWallet){
        coinWallet = await UserCoinWallet.create({userId});
    }

    return coinWallet;

}

async function getUsersWallets(filters,options) {
    let coinWallet = await UserCoinWallet.paginate(filters,options);
    return coinWallet;
}

async function getTotalHoldings(filters,options) {
    const result = await UserCoinWallet.aggregate([
        { $group: { _id: null, totalHoldings: { $sum: "$balance" } } }
      ]);

    const totalHoldings =  result.length > 0 ? result[0].totalHoldings : 0;
    return totalHoldings;
}

async function getTotalSpend(filters,options) {
    const result = await CoinWalletTransaction.aggregate([
        {$match:{transactionType:"debit"}},
        { $group: { _id: null, totalSpends: { $sum: "$coins" } } }
      ]);

    const totalSpends =  result.length > 0 ? result[0].totalSpends : 0;
    return totalSpends;
}

async function getTotalPurchase(filters,options) {
    const result = await CoinWalletTransaction.aggregate([
        {$match:{transactionType:"credit",...filters}},
        { $group: { _id: null, totalPurchase: { $sum: "$coins" } } }
      ]);

    const totalPurchase =  result.length > 0 ? result[0].totalPurchase : 0;
    return totalPurchase;
}

async function getRevenue(filters) {
    const result = await Payment.aggregate([
        {$match:{product:"coin",...filters}},
        { $group: { _id: null, totalRevenue: { $sum: "$amount" } } }
      ]);

    const totalRevenue =  result.length > 0 ? result[0].totalRevenue : 0;
    return totalRevenue;
}




module.exports = {
    createAndGetWallet,
    getUsersWallets,
    getTotalHoldings,
    getTotalSpend,
    getRevenue,
    getTotalPurchase
};