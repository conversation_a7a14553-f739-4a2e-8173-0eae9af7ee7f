const mongoose = require('mongoose');
const { paginate } = require('./plugins/paginate');

const favouriteSchema = new mongoose.Schema(
    {
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        workoutPlanId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'WorkoutPlan'
        },
        dietPlanId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'DietPlan'
        },
        type:{
            type:String,
            enum:["workout","diet"]
        }
    },
    { timestamps: true }
)

favouriteSchema.plugin(paginate);

const Favourite = mongoose.model('Favourite', favouriteSchema);
module.exports = {
    Favourite
};