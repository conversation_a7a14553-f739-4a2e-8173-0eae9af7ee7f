const mongoose = require("mongoose");
const { paginate } = require("./plugins/paginate");

const dailyReminderSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    isForAll: {
      type: Boolean,
      default: true,
    },
    type: {
      type: String,
      enum: ["WIR", "MR"],
    },
    frequency: {
      type: String,
      enum: ["daily", "hourly"],
    },
    repeatTime: {
      type: String,
    },
  },
  { timestamps: true }
);

dailyReminderSchema.plugin(paginate);

const DailyReminder = mongoose.model("DailyReminder", dailyReminderSchema);
module.exports = {
  DailyReminder,
};
