const { User, Client } = require('../models');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { Comment } = require('../models/comment.model');


async function getComments(filter) {
    return await Comment.find(filter).populate("userId").sort({ createdAt: -1 });
}

async function getCommentById(id) {
    return await Comment.findById(id);
}

async function createComment(details) {
    return await Comment.create(details);
}

async function updateCommentById(id, newDetails) {
    return await Comment.findByIdAndUpdate(id, newDetails, { new: true });
}

async function deleteCommentById(id) {

    try {
        await Comment.findByIdAndDelete(id);
        return true;
    } catch (err) {
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to delete the comment');
    }
}


module.exports = {
    getComments,
    getCommentById,
    createComment,
    updateCommentById,
    deleteCommentById
};