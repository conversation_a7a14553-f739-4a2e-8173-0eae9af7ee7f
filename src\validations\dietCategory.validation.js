const Joi = require('joi');
const { objectId } = require('./custom.validation');

const dietCategorySchema = {
    name: Joi.string(),
};


const AddDietCategory = {
    body: Joi.object().keys({
        ...dietCategorySchema,
    }),
};

const EditDietCategory = {
    body: Joi.object().keys({
        _id: Joi.string()
            .custom(objectId),
        ...dietCategorySchema,
    }),
};

module.exports = {
    AddDietCategory,
    EditDietCategory
};
