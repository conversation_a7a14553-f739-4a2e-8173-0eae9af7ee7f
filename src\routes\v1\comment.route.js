const express = require('express');
const router = express.Router();
const { firebaseAuth } = require('../../middlewares/firebaseAuth');

const commentController = require('../../controllers/comment.controller');


router.post(
    '/add',
    firebaseAuth,
    // validate(equipmentValidation.AddEquipment),
    commentController.store
);

router.post(
    '/update',
    firebaseAuth,
    // validate(equipmentValidation.EditEquipment),
    commentController.updateComment
)

router.get(
    '/list/:postId',
    firebaseAuth,
    commentController.getComments
);
router.get(
    '/:id',
    firebaseAuth,
    commentController.getComment
);

router.delete(
    '/:id',
    firebaseAuth,
    commentController.deleteComment
);

module.exports = router;