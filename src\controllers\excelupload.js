const xlsx = require("xlsx");
const { Exercise } = require("../models/exercise.model");
const catchAsync = require("../utils/catchAsync");

const { Equipment } = require("../models/equipment.model");

const mapFields = async (data) => {
  return await Promise.all(
    data.map(async (entry) => {
      const equipmentNames =
        entry["Equipment"]?.split(",").map((equip) => equip.trim()) || [];
      const equipmentIds = await Promise.all(
        equipmentNames.map(async (equipName) => {
          const equipment = await Equipment.findOneAndUpdate(
            { name: equipName },
            { $setOnInsert: { name: equipName } },
            { new: true, upsert: true, returnDocument: "after" }
          ).lean();
          return equipment._id;
        })
      );

      return {
        name: entry["ExerciseName"],
        exerciseTypes:
          entry["ExcerciseType"]?.split(",").map((type) => type.trim()) || [],
        musclegroupsinvolved:
          entry["musclegroupsinvolved"]
            ?.split(",")
            .map((group) => group.trim()) || [],
        equipment: equipmentIds.map((equip) => equip._id),
        mechanicsType: entry["Mechanics type"]?.trim(),
        level: entry["Level"]?.trim() || "Advanced",
        description: entry["Description"]?.trim(),
        reps: isNaN(entry["Reps"]) ? null : Number(entry["Reps"]),
        time: isNaN(entry["Time"]) ? null : Number(entry["Time"]),
        interval: isNaN(entry["Interval"]) ? null : Number(entry["Interval"]),
        alternativeExercises:
          entry["alternativeExercises"]?.split(",").map((e) => e.trim()) || [],

        intensity: isNaN(entry["intensity"]) ? 5 : Number(entry["intensity"]),
        exerciseObjective: entry["exerciseObjective"]?.trim() || "Endurance",

        weight: entry["Weight"]?.trim().toLowerCase() === "yes",
      };
    })
  );
};

const uploadFileAndInsertData = catchAsync(async (req, res) => {
  const file = req.file;
  const workbook = xlsx.readFile(file.path);
  const sheetName = workbook.SheetNames[0];
  const sheet = workbook.Sheets[sheetName];
  const data = xlsx.utils.sheet_to_json(sheet);

  const mappedData = await mapFields(data);

  const bulkOps = mappedData.map((entry) => ({
    updateOne: {
      filter: {
        name: entry.name,
      },
      update: { $set: entry },
      upsert: true,
    },
  }));

  await Exercise.bulkWrite(bulkOps);

  res
    .status(200)
    .json({ status: true, message: "File processed successfully!" });
});

module.exports = { uploadFileAndInsertData };
