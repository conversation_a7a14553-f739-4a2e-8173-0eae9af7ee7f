const catchAsync = require("../utils/catchAsync");
const likeService = require("../services/like.service");
const postService = require("../services/post.service");

const ApiError = require("../utils/ApiError");
const httpStatus = require("http-status");
const userNotificationModel = require("../models/userNotification.model");
const notiFunc = require('../microservices/notification.service');


const getlikes = catchAsync(async (req, res, next) => {

    try {
        const { postId } = req.params;
        const likes = await likeService.getLikes({ postId: postId });
        res.status(200).send({ data: likes, message: '' });
    } catch (error) {
        next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing Terms', error));
    }
});

const like = catchAsync(async (req, res, next) => {
    const user = req.user;
    const {postId} = req.body

    const post = await postService.getPostById(postId);

    if(!post){
        throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid Post');
    }
    const data = {userId:user._id,postId};
    let like = await likeService.getLike(data);

    if(like){
        await like.deleteOne();
        const updateDetails = {$inc: { likeCount: -1 },$pull: { likeBy: user._id }};
        const postUpdate =  await postService.updatePostById(postId,updateDetails);
    }else{
        like = await likeService.createLike(data);
        const updateDetails = {$inc: { likeCount: 1 },$addToSet: { likeBy: user._id }};
        const postUpdate =  await postService.updatePostById(postId,updateDetails);
        const notiData = {
            receiver:post.userId,
            title: `Some Has like your post.`,
            description: `${user.name} has like on your post.`,
        };
        const userNotifications = await userNotificationModel.create(notiData);
        await notiFunc.sendToTopic(post.userId?._id.toString(), { title: notiData.title, body: notiData.description });
    }

    res.status(201).send({ data: like, message: 'like is created Successfully' });
});


module.exports = {
    getlikes,
    like
}