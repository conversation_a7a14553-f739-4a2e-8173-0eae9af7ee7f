const catchAsync = require("../utils/catchAsync");
const coinOfferService = require("../services/coinOffer.service");
const ApiError = require("../utils/ApiError");
const httpStatus = require("http-status");

const getCoinOffers = catchAsync(async (req, res, next) => {
    try {
        const coinOffers = await coinOfferService.getUserCoinOffers();
        res.status(200).send({ data: coinOffers, message: '' });
    } catch (error) {
        next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing Terms', error));
    }
});

module.exports = {
    getCoinOffers,
}