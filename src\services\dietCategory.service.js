const { User, Client } = require('../models');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { fileUploadService } = require('../microservices');
const { DietCategory } = require('../models/dietCategory.model');
const { getAllData } = require("../utils/getAllData")


async function getDietCategoryById(id) {
    const dietCategory = await DietCategory.findById(id);
    return dietCategory;
}

async function getDietCategorys(filters, options) {
    try {
        // Aggregate query
        const dietCategorys = await DietCategory.paginate(filters, options);

        return dietCategorys;
    } catch (error) {
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing workoutPlans');
    }
}

async function createDietCategory(details, image) {
    let data = { ...details };

    if (image) {
        const [thumbnail] = await fileUploadService.s3Upload([image], 'thumbnail').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload thumbnail');
        })
        data = { ...data, thumbnail };
    };

    return await DietCategory.create(data);
}

async function updateDietCategoryById(id, body, image) {
    const dietCategory = await DietCategory.findById(id);
    let updates = { ...body };
    console.log(image)
    if (image) {
        const [thumbnail] = await fileUploadService.s3Upload([image], 'thumbnail').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload thumbnail');
        });
        if (dietCategory.thumbnail) {
            const oldPicKey = dietCategory.thumbnail.key;
            await fileUploadService
                .s3Delete(oldPicKey)
                .catch(err => console.log('Failed to delete thumbnail', oldPicKey));
        }
        updates = { ...updates, thumbnail };
    }

    return await DietCategory.findByIdAndUpdate(id, updates, { new: true });
}

async function deleteDietCategoryById(id) {
    try {
        await DietCategory.findByIdAndDelete(id);
        return true;
    } catch (err) {
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to delete the DietCategory');
    }
}

async function getAllDietCategorys(query, populateConfig) {
    const data = await getAllData(DietCategory, query, populateConfig)
    return data;
}

module.exports = {
    getDietCategoryById,
    getDietCategorys,
    createDietCategory,
    updateDietCategoryById,
    deleteDietCategoryById,
    getAllDietCategorys
}