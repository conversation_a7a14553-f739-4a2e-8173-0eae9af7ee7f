const mongoose = require("mongoose");
const { paginate } = require("./plugins/paginate");

// ⬇️ Reused for each exercise
const exerciseSchema = new mongoose.Schema({
  _id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Exercise",
    required: true,
  },
  name: { type: String, required: true },
  equipment: [{ type: String }],
  weight: { type: String },
  sets: { type: Number, required: true },
  reps: { type: String, required: true },
  thumbnail: { type: String, default: null },
  video: { type: String, default: null },
  caloriesBurnt: { type: String, default: null },
  duration: { type: Number, default: 0 },
});

// ⬇️ New: sections per workout day
const sectionSchema = new mongoose.Schema({
  section: { type: String, required: true },
  exercises: [exerciseSchema],
});

// ⬇️ Updated: workout day includes sections array
const workoutDaySchema = new mongoose.Schema({
  date: { type: String, required: true },
  description: { type: String, trim: true },
  sections: [sectionSchema],
  isLeave: { type: Boolean, default: false },
});

// ⬇️ Top-level workout plan schema
const customWorkoutPlanSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    workoutPlan: { type: [workoutDaySchema], required: true },
    vectorFileId: { type: String },
    description: { type: String, trim: true },
    notificationSchedule: {
      enabled: { type: Boolean, default: true },
      time: { type: String },
    },
  },
  { timestamps: true }
);

customWorkoutPlanSchema.index({ user: 1, vectorFileId: 1 });
customWorkoutPlanSchema.plugin(paginate);

const CustomWorkoutPlan = mongoose.model(
  "CustomWorkoutPlan",
  customWorkoutPlanSchema
);

module.exports = {
  CustomWorkoutPlan,
};
