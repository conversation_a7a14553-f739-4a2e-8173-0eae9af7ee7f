const express = require('express');
const router = express.Router();

const validate = require('../../../middlewares/validate');
const { adminProtect } = require('../../../middlewares/adminAuth');
const searchValidation = require("../../../validations/search.validation");

const userController = require('../../../controllers/admin/user.controller');

router.post(
    '/update',
    adminProtect,
    userController.blockUser
);

router.get(
    '/list',
    adminProtect,
    validate(searchValidation.query),
    userController.getUsers
);

router.get(
    '/:id',
    adminProtect,
    userController.getUser
);


module.exports = router;