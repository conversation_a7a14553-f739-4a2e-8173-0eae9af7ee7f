const { User, Client } = require('../models');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { fileUploadService } = require('../microservices');
const { Blog } = require('../models/blog.model');
const { Tag } = require('../models/tag.model');

async function getBlogById(id) {
    const blog = await Blog.findById(id);
    return blog;
}

async function getBlogs(filters, options) {
    return await Blog.paginate(filters, options);
}

async function createBlog(details, image) {
    let data = { ...details };

    if (Array.isArray(data.tags)) {
        data.tags.forEach(async element => {
            let tag = await Tag.findOne({ name: element });
            if (!tag) {
                await Tag.create({ name: element })
            }
        });
    }

    if (image) {
        const [thumbnail] = await fileUploadService.s3Upload([image], 'thumbnail').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload thumbnail');
        })
        data = { ...data, thumbnail };
    };

    return await Blog.create(data);
}

async function updateBlogById(id, body, image) {
    const blog = await Blog.findById(id);
    let updates = { ...body };

    if (image) {
        const [thumbnail] = await fileUploadService.s3Upload([image], 'thumbnail').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload thumbnail');
        });
        if (blog.thumbnail) {
            const oldPicKey = blog.thumbnail.key;
            await fileUploadService
                .s3Delete(oldPicKey)
                .catch(err => console.log('Failed to delete thumbnail', oldPicKey));
        }
        updates = { ...updates, thumbnail };
    }

    return await Blog.findByIdAndUpdate(id, updates, { new: true });
}

async function deleteBlogById(id) {
    try {
        await Blog.findByIdAndDelete(id);
        return true;
    } catch (err) {
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to delete the Blog');
    }
}

module.exports = {
    getBlogById,
    getBlogs,
    createBlog,
    updateBlogById,
    deleteBlogById
}