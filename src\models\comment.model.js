const mongoose = require('mongoose');
const { paginate } = require('./plugins/paginate');

const commentSchema = new mongoose.Schema(
    {
        comment: {
            type: String,
            required: true
        },
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        postId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Post'
        }

    },
    { timestamps: true }
)

commentSchema.plugin(paginate);

const Comment = mongoose.model('Comment', commentSchema);
module.exports = {
    Comment
};