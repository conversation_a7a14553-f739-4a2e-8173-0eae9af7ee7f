const { User, Client } = require('../models');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { fileUploadService } = require('../microservices');
const { Challenge } = require('../models/challenge.model');
const { ChallengeParticipate } = require('../models/challenge.participate.model');
const { CoinWalletTransaction } = require('../models/coinWalletTransaction.model');
const coinWalletTransactionService = require('./coinWalletTransaction.service');
const { default: mongoose } = require('mongoose');

async function getChallengeParticipatesById(id) {
    const challenge = await ChallengeParticipate.findById(id);
    return challenge;
}


async function getChallengeParticipatesByChallenge(id,sort = {order:1}) {
    const challenge = await ChallengeParticipate.find({challenge:id}).populate("user").sort(sort);
    return challenge;
}
async function getleaderBoard(id,sort = {order:1}) {
    const challenge = await ChallengeParticipate.aggregate([
      {
         $match: {challenge:new mongoose.Types.ObjectId(id)} 
      },
      {
        $addFields: {
          sortField: {
            $cond: { 
              if: { $eq: ["$order", null] }, 
              then: 1000000, 
              else: "$order"
            }
          }
        }
      },
      {
        $sort: { sortField: 1 }
      },
      {
        $lookup: {
          from: "users", // Name of the user collection
          localField: "user", // Field in YourModel collection
          foreignField: "_id", // Field in user collection
          as: "userData" // Name of the field to store populated user data
        }
      },
      {
        $unwind: "$userData" // Unwind the userData array
      },
      {
        $project: {
          sortField: 0 // Exclude the added sortField from the final result
        }
      }
    ]);
    return challenge;
}



async function createParticipate(details, workoutVideo) {
    let data = { ...details };

    if (workoutVideo) {
        const [video] = await fileUploadService.s3Upload([workoutVideo], 'video').catch(err => {
            console.log(err)
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload video');
        })
        data = { ...data, video };
    };

    return await ChallengeParticipate.create(data);
}

async function updateParticipate(id, body, workoutVideo) {
    const challenge = await ChallengeParticipate.findById(id);
    let updates = { ...body };

    if (workoutVideo) {
        const [video] = await fileUploadService.s3Upload(workoutVideo, 'refVideo').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload video');
        });
        if (challenge.refVideo) {
            const oldPicKey = challenge.refVideo.key;
            await fileUploadService
                .s3Delete(oldPicKey)
                .catch(err => console.log('Failed to delete video', oldPicKey));
        }

        updates = { ...updates, video };
    }
    return await ChallengeParticipate.findByIdAndUpdate(id, updates, { new: true });
}

async function distributedPrice(id, body, workoutVideo) {
    try {

        const challenge = await Challenge.findById(id);
    
        const getWinners = await ChallengeParticipate.find({challenge:id,order:{$in:[1,2,3]}});
    
        console.log(getWinners)
        const result = await Promise.all(getWinners.map(async ele => {
            let prize = 0;
            if(ele.order == 1){
                prize = challenge.prizeCoins.firstPrize;
            }else if(ele.order == 2){
                prize = challenge.prizeCoins.secondPrize;
            }else if(ele.order == 3){
                prize = challenge.prizeCoins.thirdPrize;
            }
            const coinWalletTransaction = await coinWalletTransactionService.create(ele.user,"credit",prize,null,"1st Prize",null);
    
            return coinWalletTransaction
        }))
    
        return true;
        
    } catch (error) {

        console.log(error)
        return false;
        
    }
   

}




module.exports = {
    getChallengeParticipatesById,
    getChallengeParticipatesByChallenge,
    createParticipate,
    updateParticipate,
    distributedPrice,
    getleaderBoard
}