const catchAsync = require("../utils/catchAsync");
const postService = require("../services/post.service");

const getPosts = catchAsync(async (req, res, next) => {

    const type = req.params.type;

    let filter = {};

    if (type == "User") {
        filter.userId = req.user._id;
    }

    const posts = await postService.getPosts(filter, req.query);
    res.status(200).send({ data: posts, message: '' });

});

const getPost = catchAsync(async (req, res, next) => {

    const post = await postService.getPostById(req.params.id);
    res.status(200).send({ data: post, message: '' });

});

const store = catchAsync(async (req, res, next) => {
    const user = req.user;

    const post = await postService.createPost({ ...req.body, userId: user._id }, req.file);
    res.status(201).send({ data: post, message: 'Post is created Successfully' });

});

const updatePost = catchAsync(async (req, res, next) => {
    const user = req.user;
    const post = await postService.updatePostById(req.body._id, { ...req.body, userId: user._id }, req.file);
    res.status(200).send({ data: post, message: 'Post is updated Successfully' });

});

const deletePost = catchAsync(async (req, res, next) => {

    const post = await postService.deletePostById(req.params.id);
    res.status(200).send({ data: post, message: 'Post is deleted Successfully' });

});

module.exports = {
    store,
    getPost,
    getPosts,
    updatePost,
    deletePost
}