const express = require('express');
const router = express.Router();

const validate = require('../../../middlewares/validate');
const { adminProtect } = require('../../../middlewares/adminAuth');
const exerciseValidation = require('../../../validations/exercise.validation');
const searchValidation = require('../../../validations/search.validation');

const exerciseController = require('../../../controllers/admin/exercise.controller');
const { fileUploadService } = require('../../../microservices');


router.post(
    '/add',
    adminProtect,
    fileUploadService.multerUpload.fields([
        { name: 'video', maxCount: 1 },
        { name: 'thumbnail', maxCount: 1 }
    ]),
    validate(exerciseValidation.AddWorkout),
    exerciseController.store
);

router.post(
    '/update',
    adminProtect,
    fileUploadService.multerUpload.fields([
        { name: 'video', maxCount: 1 },
        { name: 'thumbnail', maxCount: 1 }
    ]),
    validate(exerciseValidation.EditWorkout),
    exerciseController.updateExercise
);

router.get(
    '/list',
    adminProtect,
    validate(searchValidation.query),
    exerciseController.getExercises
);
router.get(
    '/:id',
    adminProtect,
    exerciseController.getExercise
);

router.delete(
    '/:id',
    adminProtect,
    exerciseController.deleteExercise
);

module.exports = router;
