const openai = require("../config/openAi");
const { calculateAge } = require("../utils/calculateAge");

async function generateWorkoutPlan(userData, exerciseList, duration, query) {
  try {
    const {
      name,
      dob,
      gender,
      height,
      weight,
      weightUnit,
      fitnessGoals,
      medicalConditions,
      motivation,
      specificFitnesstargets,
      trainingEnvironment,
      exerciseSessionDuration,
    } = userData;
    const age = calculateAge(dob);
    // Format user details
    const userInfo = `
    User Details:
    - Name: ${name}
    - Age: ${age}
    - Gender: ${gender}
    - Height: ${height} 
    - Weight: ${weight} ${weightUnit}
    - Fitness Goals: ${fitnessGoals.join(", ")}
    - Exercise Session : ${exerciseSessionDuration} minutes
    - Training Environment: ${trainingEnvironment.join(", ")}
    - Medical Conditions: ${medicalConditions.join(", ")}
    - Motivation: ${motivation}
    - Specific Fitness Targets: ${specificFitnesstargets.join(", ")}
    `;
    // Format exercises into a summary string
    const workoutSummary = exerciseList
      .map((exercise) => {
        return `_id: ${exercise._id}, Exercise name: ${exercise.name}, Equipments: ${exercise.equipment}`;
      })
      .join("\n");

    console.log(exerciseList);

    // Create system prompt based on the duration
    let systemPrompt;
    if (duration >= 30) {
      systemPrompt = `
        Create a personalized ${duration}-day workout plan for the user based on the following user details:
        ${userInfo}
    
        Here are the available exercises:
        ${workoutSummary}

        Given the user query use this as context,acknowledge the query and choose exercises accordingly 
        Here is the query :- ${query}

        Instructions:
        - Generate the workout plan by selecting **at least two appropriate exercises for each day**. Do not provide any day as a rest day.
        - Each day should have 4-5 exercises. Group exercises under each  sections (e.g., 'Warm-Up', 'Strength', 'Cardio', 'Cool-Down' ,"Push" ,"Pull" ).
        - The 'title' field in each section should describe the workout category (e.g., 'Strength', 'Cardio') and NOT include the day number.
        - Include at least 2 exercises per section, except Warm-Up and Cool-Down (1 or 2 exercises).
        - Start the response with a 'description' field containing a one-line purpose of the workout plan. Do not address the user's name or friends.
        - Generate a result for 30 days only. If the duration is more than 30 days, repeat the first 30 days for the remaining days.
        - For exercises that require weights (like Dumbbell Bench Press), include the **recommended weight (in kg)** for each exercise based on the user's main goal, fitness level, and your knowledge of strength training. Provide realistic values like 5kg, 10kg, or 15kg, depending on the user's capabilities.
        - Specify the **recommended number of sets and reps** based on the user's fitness goal, age, and gender.
        - Add the **_id** to each workout exercise.
        - Ensure workout duration matches user's exerciseSessionDuration
        - Ensure that the weight, sets, and reps are always included, even if their values are null.
        - Tailor exercise selection and intensity based on user's goals, equipment, and fitness level
        - Focus on user's targetBodyPart while maintaining overall fitness
        - Consider user's trainingEnvironment when selecting exercises
        
         #Highly important !
        - Incase of reps, if it is repetation then mention(reps) example - (10-25 reps , 2-3 reps) , if its in time give 10-20 units(seconds , minutes , hours)
        - #IMPORTANT - Dont skip a single day , provide full response for all ${duration} days , ie the plan should have all the ${duration} days
        - You cant provide examples , your job is to give a full response for all ${duration} days
        - Dont instruct to repeat , generate the complete plan for ${duration} days 
        - Dont end response without proper JSON format ending 
        - #IMPORTANT - Always end the response with proper JSON format ending , DONT SKIP A SINGLE DAY 
        - Strictly provide for all days 
        Provide the response in the following strict JSON format, grouped by days with exercises as values:
    
            {
                "description": <use duration goals and name of specificFitnesstargets to create this , do not talk about rest days>,
                "workoutPlan": {
                  "Day 1": [
                    {
                      "section": <Description of the workout category , examples Warmup , Strength , Cardio , Cool down , Push , Pull >
                      "exercises": [
                        {
                          "_id": "",
                          "name": "",
                       
                          "equipment": [],
                          "weight": "",
                          "sets": 0,
                          "reps": "",
                          "calorieBurned":<number>,
                          "duration":<in minutes>}
                        },
                          {
                          "_id": "",
                          "name": "",
                         
                          "equipment": [],
                          "weight": "",
                          "sets": 0,
                          "reps": "",
                          "calorieBurned":<number> ,
                          "duration":<in minutes>}
                        }
                      ]
                    },
                    {
                      "section": <Description of the workout category , examples Warmup , Strength , Cardio , Cool down , Push , Pull >,
                      "exercises": [
                        {
                          "_id": "",
                          "name": "",
                      
                          "equipment": [],
                          "weight": "",
                          "sets": 0,
                          "reps": "",
                          "calorieBurned":<number> ,
                          "duration":<in minutes>}
                        }
                      ]
                    }
                  ],
                  "Day 2": [
                    {
                      "section": <Description of the workout category , examples Warmup , Strength , Cardio , Cool down , Push , Pull >,
                      "exercises": [
                        {
                          "_id": "",
                          "name": "",
                      
                          "equipment": [],
                          "weight": "",
                          "sets": 0,
                          "reps": "",
                          "calorieBurned":<number> ,
                          "duration":<in minutes>}
                        }
                      ]
                    }
                  ],
                  .
                  .
                  .
                  "Day 30": [
                    {
                      "section":  <Description of the workout category , examples Warmup , Strength , Cardio , Cool down , Push , Pull >,
                      "exercises": [
                        {
                          "_id": "",
                          "name": "",
                       
                          "equipment": [],
                          "weight": "",
                          "sets": 0,
                          "reps": "",
                          "calorieBurned":<number> ,
                          "duration":<in minutes>}
                        }
                      ]
                    }
                  ]
                }
              }

    
        - **Do not include any additional fields** in the JSON other than the ones mentioned above.

      `;
    } else {
      systemPrompt = `
        Create a personalized ${duration}-day workout plan for the user based on the following user details:
        ${userInfo}
    
        Here are the available exercises:
        ${workoutSummary}
        
        Given the user query use this as context,acknowledge the query and choose exercises accordingly 
        Here is the query :- ${query}

        Instructions:
        - Generate the workout plan by selecting **at least two appropriate exercises for each day**. Do not provide any day as a rest day.
        - Generate a result for all ${duration} days.
        - Start the response with a 'description' field containing a one-line purpose of the workout plan. Do not address the user's name or friends.
        - For exercises that require weights (like Dumbbell Bench Press), include the **recommended weight (in kg)** for each exercise based on the user's main goal, fitness level, and your knowledge of strength training. Provide realistic values like 5kg, 10kg, or 15kg, depending on the user's capabilities.
        - Specify the **recommended number of sets and reps** based on the user's fitness goal, age, and gender.
        - Add the **_id** to each workout exercise.
        - Ensure workout duration matches user's exerciseSessionDuration
        - Ensure that the weight, sets, and reps are always included, even if their values are null.
        - Tailor exercise selection and intensity based on user's goals, equipment, and fitness level
        - Focus on user's targetBodyPart while maintaining overall fitness
        - Consider user's trainingEnvironment when selecting exercises
        
        #Highly important !
        - Incase of reps, if it is repetation then mention(reps) example - (10-25 reps , 2-3 reps) , if its in time give 10-20 units(seconds , minutes , hours)
        - #IMPORTANT - Dont skip a single day , provide full response for all ${duration} days , ie the plan should have all the ${duration} days
        - You cant provide examples , your job is to give a full response for all ${duration} days
        - Dont instruct to repeat , generate the complete plan for ${duration} days 
        - Dont end response without proper JSON format ending 
        - #IMPORTANT - Always end the response with proper JSON format ending , DONT SKIP A SINGLE DAY 
        - Strictly provide for all days 
        - Strictly provide for all days
        Provide the response in the following strict JSON format, grouped by days with exercises as values:
    
            {
                "description": <use duration goals and name of specificFitnesstargets to create this , do not talk about rest days>,
                "workoutPlan": {
                  "Day 1": [
                    {
                      "section": <Description of the workout category , examples Warmup , Strength , Cardio , Cool down , Push , Pull >
                      "exercises": [
                        {
                          "_id": "",
                          "name": "",
                     
                          "equipment": [],
                          "weight": "",
                          "sets": 0,
                          "reps": "",
                          "calorieBurned":<number>,
                          "duration":<in minutes>}
                        },
                          {
                          "_id": "",
                          "name": "",
                       
                          "equipment": [],
                          "weight": "",
                          "sets": 0,
                          "reps": "",
                          "calorieBurned":<number> ,
                          "duration":<in minutes>}
                        }
                      ]
                    },
                    {
                      "section": <Description of the workout category , examples Warmup , Strength , Cardio , Cool down , Push , Pull >,
                      "exercises": [
                        {
                          "_id": "",
                          "name": "",
                       
                          "equipment": [],
                          "weight": "",
                          "sets": 0,
                          "reps": "",
                          "calorieBurned":<number> ,
                          "duration":<in minutes>}
                        }
                      ]
                    }
                  ],
                  "Day 2": [
                    {
                      "section": <Description of the workout category , examples Warmup , Strength , Cardio , Cool down , Push , Pull >,
                      "exercises": [
                        {
                          "_id": "",
                          "name": "",
                    
                          "equipment": [],
                          "weight": "",
                          "sets": 0,
                          "reps": "",
                          "calorieBurned":<number> ,
                          "duration":<in minutes>}
                        }
                      ]
                    }
                  ],
                  .
                  .
                  .
                  "Day 30": [
                    {
                      "section":  <Description of the workout category , examples Warmup , Strength , Cardio , Cool down , Push , Pull >,
                      "exercises": [
                        {
                          "_id": "",
                          "name": "",
                       
                          "equipment": [],
                          "weight": "",
                          "sets": 0,
                          "reps": "",
                          "calorieBurned":<number> ,
                          "duration":<in minutes>}
                        }
                      ]
                    }
                  ]
                }
              }
    
        - **Do not include any additional fields** in the JSON other than the ones mentioned above.
      `;
    }

    const instructions =
      duration > 30
        ? `Repeat this diet pattern for the remaining ${duration - 30} days.`
        : "";

    console.log(systemPrompt);

    let data;
    try {
      const startTime = Date.now();

      const response = await openai.chat.completions.create({
        model: "gpt-4.1",
        messages: [{ role: "system", content: systemPrompt }],
        max_completion_tokens: 32768,
        temperature: 0.3,
      });

      const endTime = Date.now();
      const timeTakenMs = endTime - startTime;
      console.log(`AI response time: ${timeTakenMs} ms`);
      const responseText = response.choices[0].message.content;

      console.log("Raw AI Response:", responseText);

      //  Extract the JSON part of the response
      const jsonMatch = responseText.match(/\{.*\}/s); // Using 's' flag to handle multiline JSON
      if (!jsonMatch) {
        throw new Error("No valid JSON found in the response.");
      }

      const parsedResponse = JSON.parse(jsonMatch[0]);

      data = {
        ...parsedResponse,
      };

      // console.log("Parsed Data:", data);
    } catch (error) {
      console.error("Error processing AI response:", error.message);
      throw new Error("Failed to extract or parse JSON from the AI response.");
    }

    // Add instruction for durations longer than 30 days
    if (duration > 30) {
      data.instruction = `Repeat this workout pattern for the remaining ${duration -
        30} days.`;
    }

    const convertToFormat = (workoutPlan) => {
      return Object.entries(workoutPlan).map(([day, sections]) => ({
        day: parseInt(day.replace("Day ", ""), 10),
        sections: sections.map(({ section, exercises }) => ({
          section,
          exercises: exercises.map(
            ({
              _id,
              name,
              equipment,
              weight,
              sets,
              reps,
              thumbnail,
              video,
              calorieBurned,
              duration,
            }) => ({
              _id,
              name,
              equipment,
              weight,
              sets,
              reps,
              thumbnail,
              video,
              calorieBurned,
              duration,
            })
          ),
        })),
      }));
    };

    const output = convertToFormat(data.workoutPlan);

    // if (duration >= 30) {
    //   repeat: true,
    //   days: max(duration - 30)
    // }

    if (instructions) {
      output.instructions = instructions;
    }

    // Return the structured workout plan
    return {
      description: data.description,
      plan: output,
      instructions: output.instructions,
    };
  } catch (error) {
    throw new Error(`Failed to generate workout plan : ${error.message}`);
  }
}

module.exports = { generateWorkoutPlan };
