const admin = require("firebase-admin");

async function sendToTopic(topic, notification, data) {
  const messaging = admin.messaging();
  var payload = {
    notification,
    data,
    topic,
    android: {
      priority: "high",
      notification: { channel_id: "high_importance_channel" },
    },
  };

  try {
    console.log("Sending notification to topic:", payload);
    const msg = await messaging.send(payload);
    console.log(msg);
    return true;
  } catch (err) {
    console.log(err);
    return false;
  }
}

module.exports = {
  sendToTopic,
};
