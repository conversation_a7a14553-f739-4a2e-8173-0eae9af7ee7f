const catchAsync = require("../utils/catchAsync");
const favouriteService = require("../services/favourite.service");
const postService = require("../services/post.service");
const workoutPlanService = require("../services/workoutPlan.service");
const dietPlanService = require("../services/dietPlan.service");

const ApiError = require("../utils/ApiError");
const httpStatus = require("http-status");


const getfavourites = catchAsync(async (req, res, next) => {

    try {
        const { type } = req.query;

        const user = req.user;

        const favourites = await favouriteService.getFavourites({ userId: user._id ,type});
        res.status(200).send({ data: favourites, message: '' });
    } catch (error) {
        next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing Terms', error));
    }
});

const favourite = catchAsync(async (req, res, next) => {
    const user = req.user;
    const {workoutPlanId,dietPlanId,type} = req.body
    let query; 
    const data = {userId:user._id,type}
    if(type == "workout"){
        query = await workoutPlanService.getWorkoutPlanById(workoutPlanId);
        if(!query){
            throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid data');
        }
        data.workoutPlanId = query._id;
    }else{
        query = await dietPlanService.viewDietPlan(dietPlanId);
        if(!query){
            throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid data');
        }
        console.log(query)
        data.dietPlanId = query._id;
    }

    console.log(data)
   
    let favourite = await favouriteService.getFavourite(data);

    if(favourite){
        await favourite.deleteOne();

        const updateDetails = {$inc: { favouriteCount: -1 },$pull: { favouriteBy: user._id }};
        if(type == "workout"){
            const updateworkout = await workoutPlanService.updateWorkoutPlanById(workoutPlanId,updateDetails);
        }else{
            const updatediet = await dietPlanService.updateDietPlan(dietPlanId,updateDetails);
        }


    }else{
        favourite = await favouriteService.createFavourite(data);
        const updateDetails = {$inc: { favouriteCount: 1 },$addToSet: { favouriteBy: user._id }};
        if(type == "workout"){
            const updateworkout =  await workoutPlanService.updateWorkoutPlanById(workoutPlanId,updateDetails);
        }else{
            const updatediet =  await dietPlanService.updateDietPlan(dietPlanId,updateDetails);
        }

    }

    res.status(201).send({ data: favourite, message: 'favourite is created Successfully' });
});


module.exports = {
    getfavourites,
    favourite
}