const mongoose = require('mongoose');
const { paginate } = require('./plugins/paginate');

const postSchema = new mongoose.Schema(
    {
        image: {
            type: {
                key: String,
                url: String,
            },
            default: null,
        },
        description: {
            type: String,
            default: null,
        },
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        likeCount: {
            type: Number,
            default: 0,
        },
        likeBy:[{
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        }],
        commentCount: {
            type: Number,
            default: 0,
        }

    },
    { timestamps: true }
)

postSchema.plugin(paginate);

const Post = mongoose.model('Post', postSchema);
module.exports = {
    Post
};