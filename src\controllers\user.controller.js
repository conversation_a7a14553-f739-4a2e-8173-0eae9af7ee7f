const httpStatus = require("http-status");
const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");
const { userService } = require("../services");
const UserNotification = require("../models/userNotification.model");
const agenda = require("../config/agenda");
const moment = require("moment-timezone");
const notiFunc = require("../microservices/notification.service");
const { date } = require("joi");
const eventEmitter = require("../events/eventEmitter");
const { emitterEventNames } = require("../constants");

const updateUser = catchAsync(async (req, res) => {
  // If birthDate is present in req.body, convert it to a Date object
  if (req.body.dob) {
    const [day, month, year] = req.body.dob.split("-");
    req.body.dob = new Date(Date.UTC(year, month - 1, day));
  }
  console.log("triggered");
  const updatedUser = await userService.updateUserById(
    req.user._id,
    req.body,
    req.file
  );

  eventEmitter.emit(emitterEventNames.ASSISTANT_UPDATE, updatedUser);

  res
    .status(200)
    .send({ data: updatedUser, message: "Your details are updated" });
});

const updatePreferences = catchAsync(async (req, res) => {
  const updatedUser = await userService.updatePreferencesById(
    req.user._id,
    req.body
  );
  eventEmitter.emit(emitterEventNames.ASSISTANT_UPDATE, updatedUser);

  res
    .status(200)
    .send({ data: updatedUser, message: "Your preferences are updated" });
});

const softDeleteUser = catchAsync(async (req, res) => {
  const { userId } = req.params;
  if (req.user.__t !== "Admin" && userId !== req.user._id.toString()) {
    throw new ApiError(
      httpStatus.UNAUTHORIZED,
      "Sorry, you are not authorized to do this"
    );
  }
  await userService.markUserAsDeletedById(req.params.userId);
  res.status(200).send({
    message: "User has been removed successfully.",
  });
});

const deleteUser = catchAsync(async (req, res) => {
  await userService.deleteUserById(req.params.userId);
  res.status(200).send({
    message: "The user deletion process has been completed successfully.",
  });
});

const userDetail = catchAsync(async (req, res) => {
  const user = req.user;
  res.status(200).send({ status: true, user });
});

function getTimeDifferenceInMinutes(startTime, endTime) {
  // Parse the time strings
  const startParts = startTime.split(":");
  const endParts = endTime.split(":");

  // Create Date objects for the times
  const startDate = new Date();
  startDate.setHours(
    parseInt(startParts[0], 10),
    parseInt(startParts[1], 10),
    0,
    0
  );

  const endDate = new Date();
  endDate.setHours(parseInt(endParts[0], 10), parseInt(endParts[1], 10), 0, 0);

  // Calculate the difference in milliseconds
  const differenceInMs = endDate - startDate;

  // Convert milliseconds to minutes
  const differenceInMinutes = differenceInMs / 1000 / 60;

  return { differenceInMinutes, endDate, startDate };
}
const updateWir = catchAsync(async (req, res) => {
  const user = req.user;

  const WIR = req.body;

  const { differenceInMinutes } = getTimeDifferenceInMinutes(
    WIR.startTime,
    WIR.endTime
  );

  if (differenceInMinutes < WIR.interval) {
    throw new ApiError(httpStatus.BAD_REQUEST, "intervel time is greater");
  }

  if (user.WIR) {
    await agenda.cancel({ name: `WIR-${user._id.toString()}` });
  }

  const updatedUser = await userService.updateUserById(req.user._id, {
    WIR: WIR,
  });
  eventEmitter.emit(emitterEventNames.ASSISTANT_UPDATE, updatedUser);

  let cronExpression;
  if (WIR.interval == 30) {
    cronExpression = `*/30 * * * *`;
  } else {
    cronExpression = `0 */${WIR.interval / 60} * * *`;
  }

  const timeZone = getTimeZonesFromOffset(WIR.TimezoneOffset);
  agenda.define(`WIR-${user._id.toString()}`, async (job) => {
    const { userId } = job.attrs.data;

    const userData = await userService.getUserById(userId);

    const { startDate, endDate } = getTimeDifferenceInMinutes(
      userData.WIR.startTime,
      userData.WIR.endTime
    );

    if (
      userData &&
      userData.WIR &&
      userData.WIR.isActive &&
      startDate < Date.now() &&
      endDate > Date.now()
    ) {
      const userNotiData = {
        receiver: user._id,
        // notification:user._id,
        title: "Time for a water break! Stay hydrated for peak performance.",
        description: "Drink water now for better health.",
      };
      const userNotifications = await UserNotification.create(userNotiData);

      await notiFunc.sendToTopic(userData._id.toString(), {
        title: userNotiData.title,
        body: userNotiData.description,
      });
    }
  });

  await agenda.every(
    cronExpression,
    `WIR-${user._id.toString()}`,
    { userId: user._id },
    { timezone: timeZone }
  );

  res.status(200).send({ status: true, user: updatedUser });
});

const updateMoR = catchAsync(async (req, res) => {
  const user = req.user;

  const MoR = req.body;

  const { differenceInMinutes } = getTimeDifferenceInMinutes(
    MoR.startTime,
    MoR.endTime
  );

  if (differenceInMinutes < MoR.interval) {
    throw new ApiError(httpStatus.BAD_REQUEST, "intervel time is greater");
  }

  if (user.MoR) {
    await agenda.cancel({ name: `MOR-${user._id.toString()}` });
  }

  const updatedUser = await userService.updateUserById(req.user._id, {
    MoR: MoR,
  });
  eventEmitter.emit(emitterEventNames.ASSISTANT_UPDATE, updatedUser);

  let cronExpression;
  if (MoR.interval == 30) {
    cronExpression = `*/30 * * * *`;
  } else {
    cronExpression = `0 */${MoR.interval / 60} * * *`;
  }

  const timeZone = getTimeZonesFromOffset(MoR.TimezoneOffset);
  agenda.define(`MOR-${user._id.toString()}`, async (job) => {
    const { userId } = job.attrs.data;

    const userData = await userService.getUserById(userId);

    const { startDate, endDate } = getTimeDifferenceInMinutes(
      userData.MoR.startTime,
      userData.MoR.endTime
    );

    if (
      userData &&
      userData.MoR &&
      userData.MoR.isActive &&
      startDate < Date.now() &&
      endDate > Date.now()
    ) {
      const userNotiData = {
        receiver: user._id,
        // notification:user._id,
        title: "Time for a Movement break!",
        description: "Drink water now for better health.",
      };
      const userNotifications = await UserNotification.create(userNotiData);

      await notiFunc.sendToTopic(userData._id.toString(), {
        title: userNotiData.title,
        body: userNotiData.description,
      });
    }
  });

  await agenda.every(
    cronExpression,
    `WIR-${user._id.toString()}`,
    { userId: user._id },
    { timezone: timeZone }
  );

  res.status(200).send({ status: true, user: updatedUser });
});

const getTimeZonesFromOffset = (offsetMinute) => {
  const offsetMinutes = -offsetMinute;
  const timeZones = [];
  moment.tz.names().forEach((tz) => {
    const zoneOffset = moment.tz(tz).utcOffset();

    if (zoneOffset === offsetMinutes) {
      timeZones.push(tz);
    }
  });

  console.log(timeZones);

  return timeZones[0];
};
const userNotifications = catchAsync(async (req, res) => {
  const user = req.user;
  res.status(200).send({ status: true, user });
});

module.exports = {
  deleteUser,
  updateUser,
  softDeleteUser,
  updatePreferences,
  userDetail,
  updateWir,
  updateMoR,
};
