const { app, server } = require("./app"); // Import the app and HTTP server
const config = require("./config/config");
const logger = require("./config/logger");
const mongoose = require("mongoose");

// Connect to MongoDB
mongoose
  .connect(config.mongoose.url, config.mongoose.options)
  .then(() => {
    console.log("Connected to MongoDB");
  })
  .catch((err) => {
    console.error("Error connecting to MongoDB:", err);
  });

// Start the HTTP server
server.listen(config.port, () => {
  console.log(`Ai Top Fit app listening on port ${config.port}!`);
});

// ------------- Don't Modify  -------------

// Gracefully close the server on exit
const exitHandler = () => {
  if (server) {
    server.close(() => {
      logger.info("Server closed");
      process.exit(1);
    });
  } else {
    process.exit(1);
  }
};

// Handle unexpected errors
const unexpectedErrorHandler = (error) => {
  logger.error(error);
  exitHandler();
};

process.on("uncaughtException", unexpectedErrorHandler);
process.on("unhandledRejection", unexpectedErrorHandler);

process.on("SIGTERM", () => {
  logger.info("SIGTERM received");
  if (server) {
    server.close();
  }
});
// ------------- Don't Modify  -------------
