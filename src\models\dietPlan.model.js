const mongoose = require('mongoose');
const { paginate } = require("./plugins/paginate");
const timestampPlugin = require("./plugins/timestampPlugin");

const mealSchema = new mongoose.Schema({
  mealType: { type: String, enum: ['Breakfast', 'Lunch', 'Snacks', 'Dinner'] },
  recipes: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Recipe' }] // Array of Recipe references
});

// Diet Plan Schema
const dietPlanSchema = new mongoose.Schema({
  thumbnail: { type: String }, // URL or file path for thumbnail image
  planTypes: [{ type: String }],
  goal: [{ type: String }], // Array of goals
  name: { type: String },
  description: { type: String },
  weeklySchedule: {
    type: [{
      day: { type: String, enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] },
      meals: [mealSchema] // Array of meals for each day, each meal contains multiple recipes
    }],
    default: [
      { day: 'Monday', meals: [] },
      { day: 'Tuesday', meals: [] },
      { day: 'Wednesday', meals: [] },
      { day: 'Thursday', meals: [] },
      { day: 'Friday', meals: [] },
      { day: 'Saturday', meals: [] },
      { day: 'Sunday', meals: [] }
    ]
  },
  favouriteCount: {
    type: Number,
    default: 0,
  },
  favouriteBy:[{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
  }],
});

dietPlanSchema.plugin(paginate);
dietPlanSchema.plugin(timestampPlugin);

module.exports.DietPlan = mongoose.model("DietPlan",dietPlanSchema);
