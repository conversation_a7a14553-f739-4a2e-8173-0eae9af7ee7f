const Joi = require('joi');
const { objectId } = require('./custom.validation');

const challengeSchema = {
    title: Joi.string(),
    description: Joi.string(),
    duration: Joi.string(),
    calories: Joi.string(),
    type: Joi.string(),
    entryCoins: Joi.string(),
    reEntryCoins: Joi.string(),
    // prizeCoins: Joi.string(),
    rules: Joi.string(),
    howToDoIt: Joi.string(),
    refVideoTitle: Joi.string(),
    status: Joi.string(),
    startDate: Joi.date(),
    endDate: Joi.date(),
    firstPrize: Joi.string(),
    secondPrize: Joi.string(),
    thirdPrize: Joi.string(),
};


const AddChallenge = {
    body: Joi.object().keys({
        ...challengeSchema,
    }),
};

const EditChallenge = {
    body: Joi.object().keys({
        _id: Joi.string()
            .custom(objectId),
        ...challengeSchema,
    }),
};

module.exports = {
    AddChallenge,
    EditChallenge
};
