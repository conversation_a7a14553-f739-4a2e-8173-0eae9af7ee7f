const { User, Client } = require('../models');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { fileUploadService } = require('../microservices');
const { Exercise } = require('../models/exercise.model');

async function getExerciseById(id) {
    const exercise = await Exercise.findById(id);
    return exercise;
}

async function getExercises(sortOrder = 'desc', keyword = '', timeFrame = 'all', options) {
    try {
        let filter = {};

        // Filtering based on keyword (if provided)
        if (keyword) {
            filter.$or = [
                { name: { $regex: keyword, $options: 'i' } }, // Case-insensitive match for 'name'
                { description: { $regex: keyword, $options: 'i' } }, // Case-insensitive match for 'description'
                // Add more fields as needed for the keyword search
            ];
        }

        // Apply filters to the aggregation pipeline
        let pipeline = [];

        // Filtering based on timeFrame
        if (timeFrame !== 'all') {
            const now = new Date();
            let startDate;
            switch (timeFrame) {
                case 'today':
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    break;
                case 'thisWeek':
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay());
                    break;
                case 'thisMonth':
                    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                    break;
                case 'thisYear':
                    startDate = new Date(now.getFullYear(), 0, 1);
                    break;
                default:
                    startDate = new Date(0); // To include all dates (past and present)
            }
            filter.createdAt = { $gte: startDate, $lte: now };
        }

        // Aggregate query
        const exercise = await Exercise.paginate(filter, options);

        return exercise;
    } catch (error) {
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing workoutPlans');
    }
}

async function createExercise(details, media) {
    let data = { ...details };
    const image = media.thumbnail;
    const workoutVideo = media.video;

    if (image) {
        const [thumbnail] = await fileUploadService.s3Upload(image, 'thumbnail').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload thumbnail');
        })
        data = { ...data, thumbnail };
    };

    if (workoutVideo) {
        const [video] = await fileUploadService.s3Upload(workoutVideo, 'video').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload video');
        })
        data = { ...data, video };
    };


    return await Exercise.create(data);
}


async function updateExerciseById(id, body, media) {
    const exercise = await Exercise.findById(id);
    const Image = media.thumbnail;
    const workoutVideo = media.video;
    let updates = { ...body };

    if (Image) {
        const [thumbnail] = await fileUploadService.s3Upload(Image, 'thumbnail').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload thumbnail');
        });
        if (exercise.thumbnail) {
            const oldPicKey = exercise.thumbnail.key;
            await fileUploadService
                .s3Delete(oldPicKey)
                .catch(err => console.log('Failed to delete profile picture', oldPicKey));
        }
        updates = { ...updates, thumbnail };
    }
    if (workoutVideo) {
        const [video] = await fileUploadService.s3Upload(workoutVideo, 'video').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload video');
        });
        if (exercise.video) {
            const oldPicKey = exercise.video.key;
            await fileUploadService
                .s3Delete(oldPicKey)
                .catch(err => console.log('Failed to delete profile picture', oldPicKey));
        }

        updates = { ...updates, video };
    }
    return await Exercise.findByIdAndUpdate(id, updates, { new: true });
}

async function deleteExericeById(id) {
    try {
        await Exercise.findByIdAndDelete(id);
        return true;
    } catch (err) {
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to delete the user');
    }
}


module.exports = {
    getExercises,
    getExerciseById,
    updateExerciseById,
    deleteExericeById,
    createExercise
};