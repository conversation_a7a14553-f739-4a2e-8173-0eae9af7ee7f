const { User } = require("../models");
const { authService, favouriteService, userService } = require("../services");
const catchAsync = require("../utils/catchAsync");
const referralCodeGenerator = require("referral-code-generator");

const createNewUserObject = (newUser) => ({
  email: newUser.email,
  firebaseUid: newUser.uid,
  // profilePic: newUser.picture,
  isEmailVerified: newUser.isEmailVerified,
  firebaseSignInProvider: newUser.firebase.sign_in_provider,
});

const loginUser = catchAsync(async (req, res) => {
  const user =
    req.user.__t === "Student"
      ? await userService.getStudent(req.user._id)
      : req.user;
  res.status(200).send({ data: req.user });
});

const onBoarding = catchAsync(async (req, res) => {
  if (req.user) {
    return res.status(200).send({ data: req.user });
  } else {
    let referralUser = null;
    if (req.body.referral) {
      referralUser = await userService.getUserByReferralCode(req.body.referral);
    }

    // Parse dd/mm/yyyy date string from req.body.birthDate
    let birthDateISO = null;
    if (req.body.dob) {
      const [day, month, year] = req.body.dob.split("-");
      // month is 0-indexed in JS Date
      birthDateISO = new Date(Date.UTC(year, month - 1, day));
      // Date.UTC creates date in UTC timezone, so stored ISO will be with +00:00 offset
    }

    let referralCode = referralCodeGenerator.alphaNumeric("uppercase", 2, 4);
    const userObj = {
      ...createNewUserObject(req.newUser),
      ...req.body,
      referralCode,
      referredBy: referralUser ? referralUser._id : null,
      dob: birthDateISO, // add the parsed Date object here
    };

    let user = await User.create(userObj);

    res.status(201).send({ data: user });
  }
});

module.exports = {
  loginUser,
  onBoarding,
};
