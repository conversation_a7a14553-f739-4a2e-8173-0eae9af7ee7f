const moment = require("moment");
const UserChatHistory = require("../models/userChatHistory.model");
const { appDefaults } = require("../constants");

// async function scheduleNextMealNotification(dietPlan) {
//   const agenda = require("../config/agenda");
//   const { endDate, notificationSchedule, dietPlan: dietDays, user } = dietPlan;

//   if (!notificationSchedule.enabled) {
//     console.log(`🔕 Notifications are disabled for user: ${user}`);
//     return;
//   }

//   console.log(`🔄 Checking for the next meal notification for user: ${user}`);

//   const now = moment();
//   const end = moment(endDate, "YYYY-MM-DD").endOf("day");

//   if (now.isAfter(end)) {
//     console.log(
//       `🏁 End date reached for user: ${user}. Marking plan as finished.`
//     );
//     await CustomDietPlan.updateOne({ _id: dietPlan._id }, { isFinished: true });
//     return;
//   }

//   // Get current or next available day
//   const currentDay = dietDays.find((day) => {
//     const dayDate = moment(day.date, "YYYY-MM-DD");
//     return (
//       dayDate.isSameOrAfter(now, "day") && dayDate.isSameOrBefore(end, "day")
//     );
//   });

//   if (!currentDay) {
//     console.log(`🏁 No more valid days in plan for user: ${user}`);
//     await CustomDietPlan.updateOne({ _id: dietPlan._id }, { isFinished: true });
//     return;
//   }

//   const currentDate = moment(currentDay.date, "YYYY-MM-DD");

//   // Get all meals for the day and sort them by time
//   const meals = [
//     {
//       type: "breakfast",
//       time: notificationSchedule.breakfastTime,
//       meal: currentDay.breakfast,
//     },
//     {
//       type: "lunch",
//       time: notificationSchedule.lunchTime,
//       meal: currentDay.lunch,
//     },
//     {
//       type: "dinner",
//       time: notificationSchedule.dinnerTime,
//       meal: currentDay.dinner,
//     },
//   ]
//     .filter((m) => m.meal) // Remove empty meals
//     .sort((a, b) => {
//       // Sort by time
//       const timeA = moment(`2000-01-01 ${a.time}`);
//       const timeB = moment(`2000-01-01 ${b.time}`);
//       return timeA.diff(timeB);
//     });

//   // Find next meal to schedule
//   const nextMeal = meals.find(({ time }) => {
//     const mealTime = moment(
//       `${currentDate.format("YYYY-MM-DD")} ${time}`,
//       "YYYY-MM-DD HH:mm"
//     );
//     const triggerTime = mealTime.clone().subtract(15, "minutes");
//     return triggerTime.isAfter(now);
//   });

//   if (nextMeal) {
//     // Schedule next meal for today
//     const mealTime = moment(
//       `${currentDate.format("YYYY-MM-DD")} ${nextMeal.time}`,
//       "YYYY-MM-DD HH:mm"
//     );
//     const triggerTime = mealTime.clone().subtract(15, "minutes");

//     console.log(
//       `⏳ Scheduling ${nextMeal.type} (${
//         nextMeal.meal.meal
//       }) notification for user: ${user} at ${triggerTime.format()}`
//     );

//     await agenda.schedule(triggerTime.toDate(), "send-meal-notification", {
//       userId: user,
//       mealId: nextMeal.meal._id,
//       mealName: nextMeal.meal.meal,
//       thumbnail: nextMeal.meal.thumbnail,
//       dietPlanId: dietPlan._id,
//       currentDate: currentDate.format("YYYY-MM-DD"),
//     });

//     console.log(
//       `✅ Scheduled notification for ${
//         nextMeal.meal.meal
//       } at ${triggerTime.format()}`
//     );
//     return;
//   }

//   // If no meals left today, find the next day with meals
//   const nextDay = dietDays.find((day) => {
//     const dayDate = moment(day.date, "YYYY-MM-DD");
//     return (
//       dayDate.isAfter(currentDate, "day") &&
//       dayDate.isSameOrBefore(end, "day") &&
//       (day.breakfast || day.lunch || day.dinner)
//     );
//   });

//   if (nextDay) {
//     const firstMeal = [
//       {
//         type: "breakfast",
//         time: notificationSchedule.breakfastTime,
//         meal: nextDay.breakfast,
//       },
//       {
//         type: "lunch",
//         time: notificationSchedule.lunchTime,
//         meal: nextDay.lunch,
//       },
//       {
//         type: "dinner",
//         time: notificationSchedule.dinnerTime,
//         meal: nextDay.dinner,
//       },
//     ]
//       .filter((m) => m.meal)
//       .sort((a, b) => {
//         const timeA = moment(`2000-01-01 ${a.time}`);
//         const timeB = moment(`2000-01-01 ${b.time}`);
//         return timeA.diff(timeB);
//       })[0];

//     if (firstMeal) {
//       const nextDayDate = moment(nextDay.date, "YYYY-MM-DD");
//       const mealTime = moment(
//         `${nextDayDate.format("YYYY-MM-DD")} ${firstMeal.time}`,
//         "YYYY-MM-DD HH:mm"
//       );
//       const triggerTime = mealTime.clone().subtract(15, "minutes");

//       console.log(
//         `⏳ Scheduling next day's first meal (${
//           firstMeal.meal.meal
//         }) for user: ${user} at ${triggerTime.format()}`
//       );

//       await agenda.schedule(triggerTime.toDate(), "send-meal-notification", {
//         userId: user,
//         mealId: firstMeal.meal._id,
//         mealName: firstMeal.meal.meal,
//         thumbnail: firstMeal.meal.thumbnail,
//         dietPlanId: dietPlan._id,
//         currentDate: nextDayDate.format("YYYY-MM-DD"),
//       });

//       console.log(
//         `✅ Scheduled next day's meal: ${
//           firstMeal.meal.meal
//         } at ${triggerTime.format()}`
//       );
//     }
//   } else {
//     console.log(
//       `🏁 No more meals to schedule for user: ${user}. Marking plan as finished.`
//     );
//     await CustomDietPlan.updateOne({ _id: dietPlan._id }, { isFinished: true });
//   }
// }

const scheduleNextMealNotification = async (
  userId,
  dietPlan,
  notificationSchedule
) => {
  const agenda = require("../config/agenda");

  const today = moment()
    .tz(appDefaults.timeZone)
    .format("YYYY-MM-DD"); // Get today's date in localtime
  const now = moment().tz(appDefaults.timeZone); // Current time in localtime

  for (const day of dietPlan) {
    if (day.date < today || day.isLeave) continue; // Skip past or leave days

    const { date, breakfast, lunch, dinner } = day;
    const { breakfastTime, lunchTime, dinnerTime } = notificationSchedule;

    const meals = [
      { type: "breakfast", time: breakfastTime, details: breakfast },
      { type: "lunch", time: lunchTime, details: lunch },
      { type: "dinner", time: dinnerTime, details: dinner },
    ];

    for (const meal of meals) {
      if (!meal.details) continue; // Skip empty meals

      // Corrected: Use local timezone and subtract 15 minute
      let scheduleTime = moment
        .tz(`${date} ${meal.time}`, "YYYY-MM-DD HH:mm", appDefaults.timeZone)
        .subtract(15, "minute");

      if (scheduleTime.isAfter(now)) {
        await agenda.schedule(scheduleTime.toDate(), "send-diet-notification", {
          userId,
          meal: meal.type,
          mealDetails: meal.details,
          date,
          dietPlan,
          notificationSchedule, // Pass the entire diet plan for next scheduling
        });

        console.log(
          `Scheduled next meal notification: ${
            meal.type
          } on ${date} at ${scheduleTime.format(
            "YYYY-MM-DD HH:mm:ss A"
          )} IST for user ${userId}`
        );
        return; // Stop after scheduling the first upcoming meal
      }
    }
  }

  console.log(`No upcoming meals to schedule for user ${userId}`);
};

async function cancelExistingNotifications(userId) {
  const agenda = require("../config/agenda");

  console.log(`🛑 Cancelling existing notifications for user: ${userId}`);

  try {
    await agenda.cancel({
      "data.userId": userId,
      name: "send-meal-notification",
    });
    console.log(
      `✅ All pending meal notifications cancelled for user: ${userId}`
    );
  } catch (error) {
    console.error(`❌ Error cancelling notifications:`, error);
  }
}

const updateChatHistory = async (
  userId,
  rephrasedQuery,
  response,
  existingChatHistory
) => {
  // Prepare the new chat entry
  const newChatEntry = {
    userQuery: rephrasedQuery, // Save the query (rephrased if follow-up, else original)
    answer: JSON.stringify(response), // Save the response as a JSON string
  };

  if (existingChatHistory) {
    // Check if chat history exceeds the maximum allowed records
    if (existingChatHistory.chat.length >= 10) {
      // Remove the earliest entry (shift out the first record)
      existingChatHistory.chat.shift();
    }

    // Append the new chat entry
    existingChatHistory.chat.push(newChatEntry);
    await existingChatHistory.save(); // Save changes to the database
  } else {
    // Create a new chat history document if none exists
    const chatHistory = new UserChatHistory({
      user: userId,
      chat: [newChatEntry], // Add the new entry as the first entry
    });
    await chatHistory.save(); // Save the new document to the database
  }
};

// Helper function to generate description
const generateDescription = (dayExercises, exerciseDataMap) => {
  console.log("Day Exercises:", dayExercises);

  // Normalize IDs in Day Exercises to match Exercise Data Map
  const normalizedExerciseDataMap = {};
  Object.keys(exerciseDataMap).forEach((key) => {
    const normalizedKey = exerciseDataMap[key]._id.toString(); // Convert ObjectId to string
    normalizedExerciseDataMap[normalizedKey] = exerciseDataMap[key];
  });

  // Extract all muscle groups involved in the day's exercises
  const muscleGroups = dayExercises.flatMap((exercise) => {
    const exerciseData = normalizedExerciseDataMap[exercise._id]; // Match using normalized map

    if (!exerciseData) {
      console.warn(`Missing exercise data for ${exercise.name}`);
      return [];
    }

    if (!Array.isArray(exerciseData.musclegroupsinvolved)) {
      console.warn(
        `Invalid or missing musclegroupsinvolved for ${exercise.name}.`
      );
      return [];
    }

    return exerciseData.musclegroupsinvolved;
  });

  console.log("Extracted Muscle Groups:", muscleGroups);

  // Deduplicate and normalize muscle groups
  const uniqueMuscleGroups = [
    ...new Set(
      muscleGroups
        .filter((group) => typeof group === "string" && group.trim().length > 0)
        .map((group) => group.toLowerCase())
    ),
  ].map((group) => group.charAt(0).toUpperCase() + group.slice(1));

  console.log("Unique Muscle Groups:", uniqueMuscleGroups);

  // Define muscle group categories
  const muscleGroupCategories = {
    "lower body": [
      "Legs",
      "Glutes",
      "Hamstrings",
      "Calves",
      "Hip flexors",
      "Quadriceps",
      "Lower back",
    ],
    "upper body": ["Chest", "Back", "Shoulders", "Biceps", "Triceps", "Traps"],
    "core or full body": ["Core", "Full body", "Various", "Abs"],
  };
  console.log("Muscle Group Categories:", muscleGroupCategories);

  // Determine focus areas
  const workoutFocus = [];
  if (
    uniqueMuscleGroups.some((group) =>
      muscleGroupCategories["lower body"].includes(group)
    )
  ) {
    workoutFocus.push("lower body");
  }
  if (
    uniqueMuscleGroups.some((group) =>
      muscleGroupCategories["upper body"].includes(group)
    )
  ) {
    workoutFocus.push("upper body");
  }
  if (
    uniqueMuscleGroups.some((group) =>
      muscleGroupCategories["core or full body"].includes(group)
    )
  ) {
    workoutFocus.push("core and full body");
  }

  console.log("Workout Focus Areas:", workoutFocus);

  // Construct the description
  if (workoutFocus.length === 0) {
    console.log("Final Description: General Workout Plan");
    return "Your general workout plan includes exercises targeting various muscle groups.";
  }

  const muscleGroupDescription = uniqueMuscleGroups.join(", ");

  if (workoutFocus.length === 1) {
    const description = `Your ${workoutFocus[0]} workout plan focusing on ${muscleGroupDescription}.`;
    console.log("Final Description:", description);
    return description;
  } else {
    const description = `Your combined ${workoutFocus.join(
      " and "
    )} workout plan focuses on ${muscleGroupDescription}.`;
    console.log("Final Description:", description);
    return description;
  }
};

module.exports = {
  scheduleNextMealNotification,
  cancelExistingNotifications,
  updateChatHistory,
  generateDescription,
};
