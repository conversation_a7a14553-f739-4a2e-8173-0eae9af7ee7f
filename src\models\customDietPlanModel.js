const mongoose = require("mongoose");

const mealSchema = new mongoose.Schema({
  _id: { type: mongoose.Schema.Types.ObjectId, ref: "Recipe", required: true },
  meal: { type: String, required: true },
  servingSize: { type: String, required: true },
  thumbnail: { type: String },
  recipeVideo: { type: String },
});

const dietDaySchema = new mongoose.Schema({
  date: { type: String, required: true },
  breakfast: { type: mealSchema, required: false },
  lunch: { type: mealSchema, required: false },
  dinner: { type: mealSchema, required: false },
  isLeave: { type: Boolean, default: false },
});

const customDietPlanSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    vectorFileId: { type: String },
    endDate: { type: String, required: true },
    dietPlan: { type: [dietDaySchema], required: true },
    isFinished: { type: Boolean, default: false },

    // Notification schedule for meals
    notificationSchedule: {
      enabled: { type: Boolean, default: true },
      breakfastTime: { type: String }, // HH:mm format
      lunchTime: { type: String },
      dinnerTime: { type: String },
    },
  },
  { timestamps: true }
);
customDietPlanSchema.index({ user: 1, vectorFileId: 1 });

const CustomDietPlan = mongoose.model("CustomDietPlan", customDietPlanSchema);

module.exports = { CustomDietPlan };
