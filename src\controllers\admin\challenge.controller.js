const catchAsync = require("../../utils/catchAsync");
const challengeService = require("../../services/challenge.service");
const { challengeParticipateService } = require("../../services");
const ApiError = require("../../utils/ApiError");
const httpStatus = require("http-status");

const getChallenges = catchAsync(async (req, res, next) => {

    try {
        const { sortOrder, keyword, timeFrame, limit, page } = req.query;
        const challenges = await challengeService.getChallenges({}, keyword, timeFrame, { limit, page, sortOrder, sortBy: "createdAt" });
        res.status(200).send({ data: challenges, message: '' });
    } catch (error) {
        console.log(error);
        next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing challenges', error));
    }
});

const getChallenge = catchAsync(async (req, res, next) => {

    const challenge = await challengeService.getChallengeById(req.params.id);
    res.status(200).send({ data: challenge, message: '' });

});

const getChallengeParticates = catchAsync(async (req, res, next) => {

    const particates = await challengeParticipateService.getChallengeParticipatesByChallenge(req.params.id,{createdAt:-1});
    res.status(200).send({ data: particates, message: '' , status:true});

});


const leaderboard = catchAsync(async (req, res, next) => {

    const particates = await challengeParticipateService.getleaderBoard(req.params.id);
    res.status(200).send({ data: particates, message: '' , status:true});

});


const updateChallengeParticates = catchAsync(async (req, res, next) => {

    const particates = await challengeParticipateService.updateParticipate(req.body._id,req.body);
    res.status(200).send({ data: particates, message: '' , status:true});

});
const store = catchAsync(async (req, res, next) => {

    const body = req.body;
    const challenge = await challengeService.createChallenge({...req.body,prizeCoins: {
        firstPrize:body.firstPrize,
        secondPrize:body.secondPrize,
        thirdPrize:body.thirdPrize
    }}, req.files);
    res.status(201).send({ data: challenge, message: 'Challenge is created Successfully' });

});

const updateChallenge = catchAsync(async (req, res, next) => {
    const body = req.body;

    const challenge = await challengeService.updateChallengeById(req.body._id, {...req.body,prizeCoins: {
        firstPrize:body.firstPrize,
        secondPrize:body.secondPrize,
        thirdPrize:body.thirdPrize
    }}, req.files);
    res.status(200).send({ data: challenge, message: 'Challenge is updated Successfully' });

});


const markAsEnded = catchAsync(async (req, res, next) => {
    const body = req.body;

    const distributedPrice = await challengeParticipateService.distributedPrice(body._id);

    if(!distributedPrice){
        throw new ApiError(httpStatus.BAD_REQUEST,"Invalid data")
    }

    const challenge = await challengeService.updateChallengeById(req.body._id,{priceDistributed:1,
        IsEnded:1});
    res.status(200).send({ data: challenge, message: 'Challenge is updated Successfully' });

});

const deleteChallenge = catchAsync(async (req, res, next) => {

    const challenge = await challengeService.deleteChallengeById(req.params.id);
    res.status(200).send({ data: challenge, message: 'Challenge is deleted Successfully' });

});


module.exports = {
    store,
    getChallenge,
    getChallenges,
    updateChallenge,
    deleteChallenge,
    getChallengeParticates,
    updateChallengeParticates,
    leaderboard,
    markAsEnded
}