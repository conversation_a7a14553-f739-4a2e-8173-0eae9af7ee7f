const express = require('express');
const router = express.Router();

const validate = require('../../../middlewares/validate');
const { adminProtect } = require('../../../middlewares/adminAuth');
const dailyReminderValidation = require('../../../validations/dailyReminder.validation');
const searchValidation = require('../../../validations/search.validation');

const DailyReminderController = require('../../../controllers/admin/dailyReminder.controller');

router.get(
    '/list',
    adminProtect,
    validate(searchValidation.query),
    DailyReminderController.list
);

router.post(
    '/send',
    adminProtect,
    validate(dailyReminderValidation.AddDailyReminder),
    DailyReminderController.sendNotification
);

router.get(
    '/:id',
    adminProtect,
    DailyReminderController.detail
);

router.patch(
    '/:id',
    adminProtect,
    DailyReminderController.updateReminder
);

router.delete(
    '/delete/:id',
    adminProtect,
    DailyReminderController.deleteNotification
);

module.exports = router;