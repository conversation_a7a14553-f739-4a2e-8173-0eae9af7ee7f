const express = require("express");
const router = express.Router();

const validate = require("../../middlewares/validate");
const { firebaseAuth } = require("../../middlewares/firebaseAuth");
const userValidation = require("../../validations/user.validation");

const { userController } = require("../../controllers");
const { fileUploadService } = require("../../microservices");

router.get("/", firebaseAuth, userController.userDetail);

// for updating userDetails
router.patch(
  "/updateDetails",
  fileUploadService.multerUpload.single("profilePic"),
  firebaseAuth,
  // validate(userValidation.updateDetails),
  userController.updateUser
);

// for updating specific user preferences
router.patch(
  "/updatePreferences",
  validate(userValidation.updateUserPreferences),
  firebaseAuth,
  userController.updatePreferences
);

router.patch(
  "/updateWIR",
  firebaseAuth,
  // validate(userValidation.updateDetails),
  userController.updateWir
);

router.patch(
  "/updateMoR",
  firebaseAuth,
  // validate(userValidation.updateDetails),
  userController.updateMoR
);

// for deleting a user
router.delete(
  "/:userId",
  validate(userValidation.deleteUser),
  firebaseAuth,
  userController.deleteUser
);

// to soft delete a user
router.post(
  "/delete/:userId",
  validate(userValidation.deleteUser),
  firebaseAuth,
  userController.softDeleteUser
);

module.exports = router;
