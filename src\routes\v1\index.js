const express = require("express");
const router = express.Router();

const userRoute = require("./user.route");
const authRoute = require("./auth.route");

const userEquipmentRoute = require("./euipment.route");
const userWorkoutPlanRoute = require("./workout.plan.route");
const userChallengeRoute = require("./challenge.route");
const postRoute = require("./post.route");
const userCoinOfferRoute = require("./coinOffer.route");
const commentRoute = require("./comment.route");
const userDietPlanRoute = require("./diet.plan.route");
const coinRoute = require("./coin.route");
const stripeWebhookRoute = require("./stripeWebhook.route");
const termsRoute = require("./terms.route");
const blogRoute = require("./blog.route");
const favouriteRoute = require("./favourite.route");
const userNotificationRoute = require("./notification.route");
const bmiTimelineRoute = require("./bmiTimeline.route");
const aiRoute = require("./ai.route");
const customPlan = require("./customPlan.route");

router.use("/auth", authRoute);
router.use("/users", userRoute);
router.use("/equipment", userEquipmentRoute);
router.use("/workout-plan", userWorkoutPlanRoute);
router.use("/challenge", userChallengeRoute);
router.use("/post", postRoute);
router.use("/coinOffer", userCoinOfferRoute);
router.use("/comment", commentRoute);
router.use("/diet-plan", userDietPlanRoute);
router.use("/coin", coinRoute);
router.use("/stripeWebhook", stripeWebhookRoute);
router.use("/terms", termsRoute);
router.use("/blogs", blogRoute);
router.use("/favourite", favouriteRoute);
router.use("/notification", userNotificationRoute);
router.use("/bmiTimeline", bmiTimelineRoute);
router.use("/ai", aiRoute);
router.use("/custom", customPlan);

const adminAuthRoute = require("./admin/auth.route");
const exerciseRoute = require("./admin/exercise.route");
const planRoute = require("./admin/dietPlan.route");
const workoutPlanRoute = require("./admin/workoutPlan.route");
const equipmentRoute = require("./admin/equipment.route");
const adminBlogRoute = require("./admin/blog.route");
const challengeRoute = require("./admin/challenge.route");
const coinOfferRoute = require("./admin/coinOffer.route");
const adminUserRoute = require("./admin/user.route");
const notificationRoute = require("./admin/notification.route");
const dietCategoryRoute = require("./admin/dietCategory.route");
const adminCoinRoute = require("./admin/coin.route");
const dashboardRoute = require("./admin/dashboard.route");
const dailyReminderRoute = require("./admin/dailyReminder.route");
const termRoute = require("./admin/term.route");

router.use("/admin/auth", adminAuthRoute);
router.use("/admin/exercise", exerciseRoute);
router.use("/admin/dietplan", planRoute);
router.use("/admin/workoutPlan", workoutPlanRoute);
router.use("/admin/equipment", equipmentRoute);
router.use("/admin/blog", adminBlogRoute);
router.use("/admin/challenge", challengeRoute);
router.use("/admin/coinOffer", coinOfferRoute);
router.use("/admin/coin", adminCoinRoute);
router.use("/admin/user", adminUserRoute);
router.use("/admin/notification", notificationRoute);
router.use("/admin/term", termRoute);
router.use("/admin/diet-category", dietCategoryRoute);
router.use("/admin/dashboard", dashboardRoute);
router.use("/admin/dailyReminder", dailyReminderRoute);

module.exports = router;
