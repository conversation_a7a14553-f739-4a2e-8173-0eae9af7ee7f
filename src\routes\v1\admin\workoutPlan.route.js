const express = require('express');
const router = express.Router();

const validate = require('../../../middlewares/validate');
const { adminProtect } = require('../../../middlewares/adminAuth');
const workoutPlanValidation = require('../../../validations/workoutPlan.validation');
const searchValidation = require('../../../validations/search.validation');

const workoutPlanController = require('../../../controllers/admin/workoutPlan.controller');
const { fileUploadService } = require('../../../microservices');


router.post(
    '/add',
    adminProtect,
    fileUploadService.multerUpload.single('thumbnail'),
    validate(workoutPlanValidation.AddWorkoutPlan),
    workoutPlanController.store
);

router.post(
    '/update',
    adminProtect,
    fileUploadService.multerUpload.single('thumbnail'),
    validate(workoutPlanValidation.EditWorkoutPlan),
    workoutPlanController.updateWorkoutPlan
)

router.get(
    '/list',
    adminProtect,
    validate(searchValidation.query),
    workoutPlanController.getWorkoutPlans
);
router.get(
    '/:id',
    adminProtect,
    workoutPlanController.getWorkoutPlan
);

router.delete(
    '/:id',
    adminProtect,
    workoutPlanController.deleteWorkoutPlan
);

module.exports = router;
