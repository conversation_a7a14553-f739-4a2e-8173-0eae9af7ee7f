const { User, Client } = require('../models');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { fileUploadService } = require('../microservices');
const { Equipment } = require('../models/equipment.model');
const { getAllData } = require("../utils/getAllData")


async function getEquipmentById(id) {
    const equipment = await Equipment.findById(id);
    return equipment;
}

async function getEquipments(sortOrder = 'desc', keyword = '', timeFrame = 'all', options) {
    try {
        let filter = {};

        // Filtering based on keyword (if provided)
        if (keyword) {
            filter.$or = [
                { name: { $regex: keyword, $options: 'i' } }, // Case-insensitive match for 'name'
                // Add more fields as needed for the keyword search
            ];
        }

        // Apply filters to the aggregation pipeline
        let pipeline = [];

        // Filtering based on timeFrame
        if (timeFrame !== 'all') {
            const now = new Date();
            let startDate;
            switch (timeFrame) {
                case 'today':
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    break;
                case 'thisWeek':
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay());
                    break;
                case 'thisMonth':
                    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                    break;
                case 'thisYear':
                    startDate = new Date(now.getFullYear(), 0, 1);
                    break;
                default:
                    startDate = new Date(0); // To include all dates (past and present)
            }
            filter.createdAt = { $gte: startDate, $lte: now };
        }

        // Aggregate query
        const equipments = await Equipment.paginate(filter, options);

        return equipments;
    } catch (error) {
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing workoutPlans');
    }
}

async function createEquipment(details, image) {
    let data = { ...details };

    if (image) {
        const [thumbnail] = await fileUploadService.s3Upload([image], 'thumbnail').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload thumbnail');
        })
        data = { ...data, thumbnail };
    };

    return await Equipment.create(data);
}

async function updateEquipmentById(id, body, image) {
    const equipment = await Equipment.findById(id);
    let updates = { ...body };

    if (image) {
        const [thumbnail] = await fileUploadService.s3Upload([image], 'thumbnail').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload thumbnail');
        });
        if (equipment.thumbnail) {
            const oldPicKey = equipment.thumbnail.key;
            await fileUploadService
                .s3Delete(oldPicKey)
                .catch(err => console.log('Failed to delete thumbnail', oldPicKey));
        }
        updates = { ...updates, thumbnail };
    }

    return await Equipment.findByIdAndUpdate(id, updates, { new: true });
}

async function deleteEquipmentById(id) {
    try {
        await Equipment.findByIdAndDelete(id);
        return true;
    } catch (err) {
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to delete the Equipment');
    }
}

async function getAllEquipments(query, populateConfig) {
    const data = await getAllData(Equipment, query, populateConfig)
    return data;
}

module.exports = {
    getEquipmentById,
    getEquipments,
    createEquipment,
    updateEquipmentById,
    deleteEquipmentById,
    getAllEquipments
}