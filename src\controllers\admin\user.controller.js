const catchAsync = require("../../utils/catchAsync");
const userService = require("../../services/user.service");
const postService = require("../../services/post.service");
const { getPaginateConfig } = require("../../utils/queryPHandler");


const getUsers = catchAsync(async (req, res, next) => {

    const {filters,options} = getPaginateConfig(req.query);
 
    if(filters.search){
        filters.$or = [
            {name:{$regex:filters.search,$options:"i"}},
        ];
        delete filters.search;
    }

    if(filters.timeFrame){

        let start;
        let end;
        const today = new Date();
        if(filters.timeFrame == "today"){
            start = new Date();
            start.setHours(0, 0, 0, 0);

            end = new Date();
            end.setHours(23, 59, 59, 999);
        }else if(filters.timeFrame == "thisMonth"){
            start = new Date(today.getFullYear(), today.getMonth(), 1);
            start.setHours(0, 0, 0, 0);
            
            end = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            end.setHours(23, 59, 59, 999);
        }else if(filters.timeFrame == "thisWeek"){
            start = new Date(today.setDate(today.getDate() - today.getDay()));
            start.setHours(0, 0, 0, 0);

            end = new Date(start);
            end.setDate(end.getDate() + 6);
            end.setHours(23, 59, 59, 999);
        }else if(filters.timeFrame == "thisYear"){
            start = new Date(today.getFullYear(), 0, 1);
            start.setHours(0, 0, 0, 0);
            
            end = new Date(today.getFullYear(), 11, 31);
            end.setHours(23, 59, 59, 999);
        }

        if(filters.timeFrame != "all"){
            filters.createdAt = { $gte: start, $lte: end }
        }

        delete filters.timeFrame;
    }

    const users = await userService.getUsers(filters, options);
    
    res.status(200).send({ data: users, message: '' });

});

const getUser = catchAsync(async (req, res, next) => {

    const user = await userService.getUserById(req.params.id);

    const posts = await postService.getPosts({userId:user._id},{});
    res.status(200).send({ data: user,posts, message: '' });

});

const blockUser = catchAsync(async (req, res, next) => {
    console.log(req.body);
    const user = await userService.blockUserById(req.body._id, req.body);
    res.status(200).send({ data: user, message: '' });

});

module.exports = {
    getUser,
    getUsers,
    blockUser
}