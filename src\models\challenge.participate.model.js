const mongoose = require('mongoose');
const { paginate } = require('./plugins/paginate');

const challengeParticipateSchema = new mongoose.Schema(
    {
        video: {
            type: {
                key: String,
                url: String,
            },
            default: null,
        },
        user: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "User",
        },
        challenge: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Challenge",
        },
        duration: {
            type: Number,
            default: null,
        },
        order: {
            type: Number,
            default: null
        },
        entryCoins: {
            type: Number,
            default: 0,
        },
        attempt:{
            type: Number,
            default: 1,
        },
        status: {
            type: String,
            enum: ["accepted", "rejected","pending"],
            default: "pending"
        },
        lastAttempAt:{
            type:Date
        }
    },
    { timestamps: true }
)

challengeParticipateSchema.plugin(paginate);



const ChallengeParticipate = mongoose.model('ChallengeParticipate', challengeParticipateSchema);
module.exports = {
    ChallengeParticipate
};