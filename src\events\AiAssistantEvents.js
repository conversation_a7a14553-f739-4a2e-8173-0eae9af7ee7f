const { vectorStoreService } = require("../aiServices");
const { emitterEventNames } = require("../constants");
const { CustomDietPlan } = require("../models/customDietPlanModel");
const { CustomWorkoutPlan } = require("../models/customWorkoutModel");

const catchEventHandler = require("../utils/catchEventHandler");

const handleUpdateAssistant = catchEventHandler(async (userData) => {
  try {
    if (userData.assistantId) {
      console.log("triggered");
      const [workout_plan, diet_plan] = await Promise.all([
        CustomWorkoutPlan.findOne({ user: userData._id }),
        CustomDietPlan.findOne({ user: userData._id }),
      ]);
      const vectorStore = await vectorStoreService.getOrCreateVectorStore();

      const instructions = vectorStoreService.createInstructions(
        userData,
        workout_plan.vectorFileId || "not-avaliable",
        diet_plan.vectorFileId || "not-avaliable"
      );

      await vectorStoreService.updateAssistant(
        userData.assistantId,
        vectorStore.vectorStoreId,
        instructions,
        userData._id
      );
      console.log("✅ Assistant updated after user details change");
    }
  } catch (error) {
    console.error(
      "❌ Error updating assistant after user details change:",
      error.message
    );
  }
});

module.exports = (emitter) => {
  emitter.on(emitterEventNames.ASSISTANT_UPDATE, handleUpdateAssistant);
};
