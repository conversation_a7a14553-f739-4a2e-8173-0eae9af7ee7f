const Joi = require('joi');
const { objectId } = require('./custom.validation');

const workoutPlanSchema = {
    name: Joi.string(),
    description: Joi.string(),
    duration: Joi.string(),
    caloriesBurnt: Joi.string(),
    idealFor: Joi.string(),
    type: Joi.string(),
    equipment: Joi.array(),
    section: Joi.array()
};


const AddWorkoutPlan = {
    body: Joi.object().keys({
        ...workoutPlanSchema,
    }),
};

const EditWorkoutPlan = {
    body: Joi.object().keys({
        _id: Joi.string()
            .custom(objectId),
        ...workoutPlanSchema,
    }),
};

module.exports = {
    AddWorkoutPlan,
    EditWorkoutPlan
};
