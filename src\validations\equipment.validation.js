const Joi = require('joi');
const { objectId } = require('./custom.validation');

const equipmentSchema = {
    name: Joi.string(),
};


const AddEquipment = {
    body: Joi.object().keys({
        ...equipmentSchema,
    }),
};

const EditEquipment = {
    body: Joi.object().keys({
        _id: Joi.string()
            .custom(objectId),
        ...equipmentSchema,
    }),
};

module.exports = {
    AddEquipment,
    EditEquipment
};
