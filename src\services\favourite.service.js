const { User, Client } = require('../models');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { Favourite } = require('../models/favourite.model');


async function getFavourites(filter) {
    return await Favourite.find(filter).sort({ createdAt: -1 }).populate("workoutPlanId").populate("dietPlanId");
}

async function getFavourite(data) {
    return await Favourite.findOne(data);
}

async function createFavourite(details) {
    return await Favourite.create(details);
}

async function deleteFavouriteById(data) {

    try {
        await Favourite.deleteOne(data);
        return true;
    } catch (err) {
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to delete the Favourite');
    }
}


module.exports = {
    getFavourites,
    getFavourite,
    createFavourite,
    deleteFavouriteById
};