const mongoose = require('mongoose');
const { paginate } = require("./plugins/paginate");
const timestampPlugin = require("./plugins/timestampPlugin");

// Ingredient Schema
const ingredientSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  type: {
    type: String,
    required: true
  },
  calories: {
    type: Number,
    required: true
  },
  measurementQuantity: {
    type: Number,
    required: true
  },
  measurementUnit: {
    type: String,
    required: true
  },
  thumbnail: {
    type: {
        key: String,
        url: String,
    },
    default: null,
  },
});

ingredientSchema.plugin(paginate);
ingredientSchema.plugin(timestampPlugin);

module.exports.Ingredient = mongoose.model("Ingredient",ingredientSchema);