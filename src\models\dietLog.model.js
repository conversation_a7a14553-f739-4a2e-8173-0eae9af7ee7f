const mongoose = require('mongoose');
const { paginate } = require('./plugins/paginate');

const dietLogSchema = new mongoose.Schema(
    {
        recipeId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Recipe'
        },
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        dietPlanId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'DietPlan'
        },
        Kcal: {
            type: Number,
            default:0
        },
        protien:{
            type: Number,
            default:0
        },
        carbs:{
            type: Number,
            default:0
        },
        foodName:{
            type:String
        },
        type:{
            type:String,
            enum:["recipe","custom"]
        }

    },
    { timestamps: true }
)

dietLogSchema.plugin(paginate);

const DietLog = mongoose.model('DietLog', dietLogSchema);
module.exports = {
    DietLog
};