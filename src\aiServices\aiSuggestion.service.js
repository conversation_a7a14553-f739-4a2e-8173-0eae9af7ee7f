const openai = require("../config/openAi");

async function processQuery(query) {
  try {
    // Input validation

    if (!query || query.trim() === "") {
      throw new Error("Input query is empty. Please provide a valid query.");
    }

    // Define the prompt for categorizing the query
    const prompt = `
    You are an AI assistant specialized in responding to workout, diet, meal, and fitness-related queries. Analyze the user's query and classify it into one of three primary categories:
    
    ## 🟠 1. Casual Inquiry (No Plans Created or Requested):
    - The user is making a general statement, expressing a problem, or asking for general knowledge without requesting new plans or existing plans.
    - **Use:** { "value": 0, "meal": 0, "workout": 0, "workoutPlan": 0, "dietPlan": 0 }
    
    **Examples:**
    - "I feel tired after workouts." (Sharing a problem, not asking for a solution)  
    - "What foods are high in protein?" (General knowledge, no plan creation needed)  
    
    ---
    
    ## 🟡 2. Create New Plans (Meal/Workout):
    - The user is requesting new workout or diet suggestions (even if they mention duration).  
    - **Use \`meal\` or \`workout\` for new plans.**  
    - If both are requested, use both \`meal\` and \`workout\`.  
    - **Never use \`workoutPlan\` or \`dietPlan\` for new plans.**  
    - **Use:** { "value": 1, "meal": 1 or 0, "workout": 1 or 0, "workoutPlan": 0, "dietPlan": 0 }
    
    **Examples:**
    - "Can you create a 30-day diet plan?" → { "value": 1, "meal": 1, "workout": 0, "workoutPlan": 0, "dietPlan": 0 }  
      *(New diet plan request uses \`meal\`, not \`dietPlan\`)*  
    
    - "Suggest some exercises for home workouts." → { "value": 1, "meal": 0, "workout": 1, "workoutPlan": 0, "dietPlan": 0 }  
      *(New workout request uses \`workout\`, not \`workoutPlan\`)*  
    
    - "Create a 12-week workout and diet plan for fat loss." → { "value": 1, "meal": 1, "workout": 1, "workoutPlan": 0, "dietPlan": 0 }  
      *(New combined request uses \`meal\` and \`workout\` together)*  
    
    ---
    
    ## 🟢 3. Inquiry About Existing Plans (WorkoutPlan/DietPlan):
    - The user is asking about their **existing structured plans**, not requesting new ones.  
    - Use \`workoutPlan\` or \`dietPlan\` (depending on which plan they are asking about).  
    - If both existing plans are requested, use both \`workoutPlan\` and \`dietPlan\`.  
    - **Never use \`meal\` or \`workout\` for existing plans.**  
    - **Use:** { "value": 1, "meal": 0, "workout": 0, "workoutPlan": 1 or 0, "dietPlan": 1 or 0 }
    
    **Examples:**
    - "What is my current diet plan?" → { "value": 1, "meal": 0, "workout": 0, "workoutPlan": 0, "dietPlan": 1 }  
      *(Existing diet plan inquiry uses \`dietPlan\`, not \`meal\`)*  
    
    - "Can you show me my current workout routine?" → { "value": 1, "meal": 0, "workout": 0, "workoutPlan": 1, "dietPlan": 0 }  
      *(Existing workout plan inquiry uses \`workoutPlan\`, not \`workout\`)*  
    
    - "What are my current workout and diet plans?" → { "value": 1, "meal": 0, "workout": 0, "workoutPlan": 1, "dietPlan": 1 }  
      *(Both existing plans inquiry uses \`workoutPlan\` and \`dietPlan\` together)*  
    
    ---
    
    ## 🚫 **Strict Field Rules Recap:**
    1. **Only \`meal\` and \`workout\` are for new plans.**  
    2. **Only \`workoutPlan\` and \`dietPlan\` are for existing plans.**  
    3. **\`meal\` and \`dietPlan\` cannot both be \`1\` together.**  
    4. **\`workout\` and \`workoutPlan\` cannot both be \`1\` together.**  
    5. **\`value\` is \`1\` only for actionable requests (create or inquire about plans).**  
    
    ---
    
    ## 📌 **Final Instruction:**  
    **Respond only with the appropriate JSON object based on the user query. Do not add any additional text or explanations.**  
    
    User Details:  
    Query: ${query.trim()}
    `;

    // Predefined valid responses
    const validResponses = {
      // 1. Casual inquiries
      "0,0,0,0,0": {
        value: 0,
        meal: 0,
        workout: 0,
        workoutPlan: 0,
        dietPlan: 0,
      },

      // 2. New meal and workout suggestions (creation)
      "1,1,1,0,0": {
        value: 1,
        meal: 1,
        workout: 1,
        workoutPlan: 0,
        dietPlan: 0,
      },

      // 3. New workout suggestions only
      "1,0,1,0,0": {
        value: 1,
        meal: 0,
        workout: 1,
        workoutPlan: 0,
        dietPlan: 0,
      },

      // 4. New meal suggestions only
      "1,1,0,0,0": {
        value: 1,
        meal: 1,
        workout: 0,
        workoutPlan: 0,
        dietPlan: 0,
      },

      // 5. Existing diet plan inquiry
      "1,0,0,0,1": {
        value: 1,
        meal: 0,
        workout: 0,
        workoutPlan: 0,
        dietPlan: 1,
      },

      // 6. Existing workout plan inquiry
      "1,0,0,1,0": {
        value: 1,
        meal: 0,
        workout: 0,
        workoutPlan: 1,
        dietPlan: 0,
      },

      // 7. Existing combined plan inquiry
      "1,0,0,1,1": {
        value: 1,
        meal: 0,
        workout: 0,
        workoutPlan: 1,
        dietPlan: 1,
      },
    };

    // Call OpenAI API
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content:
            "You are an AI assistant trained to identify and categorize queries about workouts and diet plans.",
        },
        { role: "user", content: prompt },
      ],
      temperature: 0.7,
    });

    // Extract the AI's response
    // Extract the AI's response
    const aiResponse = response.choices[0].message.content.trim();
    console.log(aiResponse, "<<<<<<<<<<<");

    let parsedResponse;

    try {
      // Try parsing the AI response as JSON
      parsedResponse = JSON.parse(aiResponse);
    } catch (error) {
      throw new Error(`AI response is not a valid JSON: ${aiResponse}`);
    }

    // Validate the parsed object
    const { value, meal, workout, workoutPlan, dietPlan } = parsedResponse;

    // Validate the required fields
    if (
      typeof value !== "number" ||
      typeof meal !== "number" ||
      typeof workout !== "number" ||
      typeof workoutPlan !== "number" ||
      typeof dietPlan !== "number"
    ) {
      throw new Error(`AI response is missing expected fields: ${aiResponse}`);
    }

    // Create a key for lookup
    const responseKey = `${value},${meal},${workout},${workoutPlan},${dietPlan}`;

    // Retrieve the validated response
    const validatedResponse = validResponses[responseKey];

    if (!validatedResponse) {
      throw new Error(`Unexpected classification values: ${responseKey}`);
    }

    return validatedResponse;
  } catch (error) {
    throw new Error(`Failed to process the query : ${error.message}`);
  }
}

module.exports = { processQuery };
