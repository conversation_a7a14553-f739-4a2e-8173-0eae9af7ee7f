const Joi = require('joi');
const { objectId, validateSpecialChar } = require('./custom.validation');

const dailyReminderSchema = {
    title: Joi.string(),
    description: Joi.string(),
    isForAll: Joi.boolean(),
    type:Joi.string(),
    frequency:Joi.string(),
    repeatTime:Joi.string(),
    TimezoneOffset: Joi.number(),
};


const AddDailyReminder = {
    body: Joi.object().keys({
        ...dailyReminderSchema,
    }),
};


module.exports = {
    AddDailyReminder
};
