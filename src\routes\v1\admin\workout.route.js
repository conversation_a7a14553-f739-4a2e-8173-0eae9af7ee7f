const express = require('express');
const router = express.Router();

const validate = require('../../../middlewares/validate');
const {adminProtect} = require('../../../middlewares/adminAuth');
const workoutValidation = require('../../../validations/workout.validation');

const workoutController = require('../../../controllers/admin/workout.controller');
const { fileUploadService } = require('../../../microservices');


router.post(
  '/add',
  adminProtect,
  fileUploadService.multerUpload.single('video'),
  validate(workoutValidation.AddWorkout),
  workoutController.store
);

router.post(
    '/update',
    adminProtect,
    fileUploadService.multerUpload.single('video'),
    validate(workoutValidation.EditWorkout),
    workoutController.updateWorkout
);

router.get(
    '/list/:cat',
    adminProtect,
    workoutController.getWorkouts
);
router.get(
    '/:id',
    adminProtect,
    workoutController.getWorkout
);

module.exports = router;
