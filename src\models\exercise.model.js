const mongoose = require("mongoose");
const { paginate } = require("./plugins/paginate");

const exerciseSchema = new mongoose.Schema(
  {
    thumbnail: {
      type: {
        key: String,
        url: String,
      },
      default: null,
    },
    video: {
      type: {
        key: String,
        url: String,
      },
      default: null,
    },
    name: {
      type: String,
      default: null,
    },
    description: {
      type: String,
      default: null,
    },
    equipment: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Equipment",
      },
    ],

    //-----------------------adding these field---------------------------//

    exerciseTypes: [
      {
        type: String,
        required: true,
      },
    ],
    musclegroupsinvolved: [
      {
        type: String,
        required: true,
      },
    ],
    mechanicsType: {
      type: String,
      enum: ["Compound", "Isolation"],
      required: true,
    },

    level: {
      type: String,
      enum: ["Intermediate", "Basic", "Advanced"],
      required: true,
    },

    weight: {
      type: Boolean,
      default: true,
    },
    reps: {
      type: Number,
      default: null,
    },
    time: {
      type: Number,
      default: null,
    },
    interval: {
      type: Number,
      default: null,
    },

    alternativeExercises: [String],

    intensity: {
      type: Number,
      min: 1,
      max: 10,
      default: 5,
    },

    exerciseObjective: {
      type: String,
      enum: ["Fat Loss", "Muscle Gain", "Endurance", "Flexibility"],
      default: "Endurance",
    },
  },

  { timestamps: true }
);

exerciseSchema.plugin(paginate);

const Exercise = mongoose.model("Exercise", exerciseSchema);
module.exports = {
  Exercise,
};
