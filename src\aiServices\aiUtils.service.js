const openai = require("../config/openAi");

const isFollowUp = async (conversation, latestInput) => {
  // Initialize the messages array with a system prompt
  const messages = [
    {
      role: "system",
      content:
        "Determine if the latest message is a follow-up question based on the previous conversation history between the user and assistant. A follow-up question is one that references the topic or context of any previous messages, if the message asks explicitily asks to generate or create meal or workout plan or asks to guide or help in creating one it will not be a follow-up , even if its related to follow up but mentions suggesions or help in choosing or creating a workout plan or exercise it will not be considered a follow-up , anything that requires to give a plan will not be considered a follow up",
    },
  ];

  // Extract the last 4 chat entries from the conversation and format them for the AI
  conversation.chat.slice(-4).forEach((entry, index) => {
    messages.push({
      role: "user",
      content: `User Message ${index + 1}: '${entry.userQuery}'`,
    });
    messages.push({
      role: "assistant",
      content: `Assistant Response ${index + 1}: '${entry.answer}'`,
    });
  });

  // Add the latest user input to the conversation
  messages.push({ role: "user", content: `Latest Message: '${latestInput}'` });

  // Add a direct query to the system
  messages.push({
    role: "user",
    content:
      "Does the latest message refer to the context or topics in any of the previous messages? Answer 'Yes' if it is a follow-up question, or 'No' if it is not.",
  });

  // Call gpt-4o to determine if it's a follow-up
  const response = await openai.chat.completions.create({
    model: "gpt-4o",
    messages: messages,
    max_tokens: 10,
    temperature: 0, // No randomness in the response
  });

  // Extract and trim the response
  const answer = response.choices[0].message.content.trim();

  // Return true if the response is "Yes", otherwise false
  return answer === "Yes";
};

const rephraseAsStandalone = async (conversation, latestInput) => {
  const messages = [
    {
      role: "system",
      content:
        "Rephrase the latest message as a standalone question with full context using the previous conversation history. add as much context as possible , pick all the crucial information , there is no character restriction , if its a tricky question but , doest need the context make it a stand alone question",
    },
  ];

  // Process the last 4 entries from the chat array
  conversation.chat.slice(-4).forEach((entry, index) => {
    messages.push({
      role: "user",
      content: `User Message ${index + 1}: '${entry.userQuery}'`,
    });
    messages.push({
      role: "assistant",
      content: `Assistant Response ${index + 1}: '${entry.answer}'`,
    });
  });

  // Add the latest input to the messages
  messages.push({ role: "user", content: `Latest Message: '${latestInput}'` });
  messages.push({
    role: "user",
    content:
      "Please rephrase the latest message into a standalone question with complete details from the previous conversation.",
  });

  console.log(messages);

  // Call gpt-4o API for rephrasing
  const response = await openai.chat.completions.create({
    model: "gpt-4o",
    messages: messages,
    max_tokens: 50,
    temperature: 0, // Deterministic response
  });

  // Extract and return the rephrased standalone question
  return response.choices[0].message.content.trim();
};

async function processConversation(conversationHistory, latestMessage) {
  const isFollowUpResult = await isFollowUp(conversationHistory, latestMessage);
  const outputData = {};

  if (isFollowUpResult) {
    outputData.followup = 1;
    outputData.rephrase = await rephraseAsStandalone(
      conversationHistory,
      latestMessage
    );
  } else {
    outputData.followup = 0;
    outputData.rephrase = "NA";
  }
  console.log(outputData);
  return outputData;
}

module.exports = { processConversation, rephraseAsStandalone };
