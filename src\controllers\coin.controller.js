const catchAsync = require("../utils/catchAsync");
const coinOfferService = require("../services/coinOffer.service");
const ApiError = require("../utils/ApiError");
const httpStatus = require("http-status");
const { createSubscriptionCheckout } = require("../microservices/stripe.service");
const { Payment } = require("../models/payment.model");
const { CoinWalletTransaction } = require("../models/coinWalletTransaction.model");
const { userCoinWalletService } = require("../services");

const buyCoins = catchAsync(async (req, res, next) => {
    
    const user = req.user;
    const body = req.body;

    let lineItems;
    let coins;
    if(body.coinOffer){
        const coinOffer = await coinOfferService.getCoinOfferById(body.coinOffer);
        lineItems = [
            {
              price_data: {
                currency: 'usd',
                product_data: {
                  name:coinOffer.title
                },
                unit_amount: coinOffer.price * 100,
              },
              quantity: 1,
            },
          ];

          coins = coinOffer.coinNumber;
    }else{
        lineItems = [
            {
              price_data: {
                currency: 'usd',
                product_data: {
                  name:"coins"
                },
                unit_amount: body.price * 100,
              },
              quantity: 1,
            },
        ];
        coins = body.coins;
    }

    var host = req.get('host');
    const url = req.protocol + '://' + host;

    const stripeRes = await createSubscriptionCheckout(user,lineItems,"payment",url);
    if(stripeRes.status == false){
        return res.status(500).json(stripeRes);
    }

    const payment = await Payment.create({
        sessionId:stripeRes.data.id,
        user:user._id,
        status: "pending",
        mode:stripeRes.data.mode,
        amount:stripeRes.data.amount_total/100,
        payment_status:stripeRes.data.payment_status,
        coins:coins,
        metadata:{
            coinOffer:body.coinOffer,
            type: body.coinOffer ?  "coinOffer" : "manual"
        }
    });

    return res.status(200).json({...stripeRes});

});

const walletBalance = catchAsync(async (req, res, next) => {

    const user = req.user;

    const wallet = await userCoinWalletService.createAndGetWallet(user._id);

    return res.status(200).json({
        status:true,
        wallet
    });

});

const walletTransactions = catchAsync(async (req, res, next) => {

    const user = req.user;

    const transactions = await CoinWalletTransaction.find({userId:user._id});

    return res.status(200).json({
        status:true,
        transactions
    });

})

module.exports = {
    buyCoins,
    walletBalance,
    walletTransactions
}