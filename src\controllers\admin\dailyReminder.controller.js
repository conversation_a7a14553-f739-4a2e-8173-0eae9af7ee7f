const httpStatus = require("http-status");
const catchAsync = require("../../utils/catchAsync");
const dailyReminderService  = require("../../services/dailyReminder.service");
const notiFunc = require('../../microservices/notification.service');
const { getPaginateConfig } = require("../../utils/queryPHandler");
const { User } = require("../../models");
const UserNotification = require("../../models/userNotification.model");
const agenda = require("../../config/agenda");
const moment = require('moment-timezone');
const ApiError = require("../../utils/ApiError");


const list = catchAsync(async (req, res) => {
    const {filters,options} = getPaginateConfig(req.query);
 
    if(filters.search){
        filters.$or = [
            {title:{$regex:filters.search,$options:"i"}},
            {description:{$regex:filters.search,$options:"i"}}
        ];
        delete filters.search;
    }

    if(filters.timeFrame){

        let start;
        let end;
        const today = new Date();
        if(filters.timeFrame == "today"){
            start = new Date();
            start.setHours(0, 0, 0, 0);

            end = new Date();
            end.setHours(23, 59, 59, 999);
        }else if(filters.timeFrame == "thisMonth"){
            start = new Date(today.getFullYear(), today.getMonth(), 1);
            start.setHours(0, 0, 0, 0);
            
            end = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            end.setHours(23, 59, 59, 999);
        }else if(filters.timeFrame == "thisWeek"){
            start = new Date(today.setDate(today.getDate() - today.getDay()));
            start.setHours(0, 0, 0, 0);

            end = new Date(start);
            end.setDate(end.getDate() + 6);
            end.setHours(23, 59, 59, 999);
        }else if(filters.timeFrame == "thisYear"){
            start = new Date(today.getFullYear(), 0, 1);
            start.setHours(0, 0, 0, 0);
            
            end = new Date(today.getFullYear(), 11, 31);
            end.setHours(23, 59, 59, 999);
        }

        if(filters.timeFrame != "all"){
            filters.createdAt = { $gte: start, $lte: end }
        }

        delete filters.timeFrame;
    }

    const reminders = await dailyReminderService.getDailyReminders(filters,options);
    res.status(200).json({ status: true, data: reminders })
});

const detail = catchAsync(async (req, res) => {
    const {id} = req.params;
    const reminder = await dailyReminderService.getDailyReminderById(id);
    res.status(200).json({ status: true, data: reminder })
});

const sendNotification = catchAsync(async (req, res) => {

    // const user = req.user;
    const body = req.body;


    const previousReminder = await dailyReminderService.getDailyReminder({type:body.type});
    if(previousReminder.length > 0){
        throw new ApiError(httpStatus.BAD_REQUEST,"You can not make more than 1 reminder of a same type.");
    }

    const reminder = await dailyReminderService.createDailyReminder(body);

    // await notiFunc.sendToTopic(topic, { title: body.title, body: body.description });

    let cronExpression;
    if(reminder.frequency == "daily"){
        const [hours, minutes] = body.repeatTime.split(":");
        cronExpression = `${minutes} ${hours} * * *`;
    }else{
        cronExpression = `0 */${body.repeatTime} * * *`;
    }

    const timeZone = getTimeZonesFromOffset(body.TimezoneOffset);

    await agenda.every(cronExpression, reminder.type, {reminder:reminder._id},{timezone:timeZone});

    return res.status(200).json({ status: true, reminder })
});


const updateReminder = catchAsync(async (req, res) => {

    // const user = req.user;
    const body = req.body;
    const {id} = req.params;


    // const reminder = await dailyReminderService.getDailyReminder({type:body.type});
    // if(previousReminder.length > 0){
    //     throw new ApiError(httpStatus.BAD_REQUEST,"You can not make more than 1 reminder of a same type.");
    // }

    const reminder = await dailyReminderService.updateDailyReminderById(id,body);

    // await notiFunc.sendToTopic(topic, { title: body.title, body: body.description });

    let cronExpression;
    if(reminder.frequency == "daily"){
        const [hours, minutes] = body.repeatTime.split(":");
        cronExpression = `${minutes} ${hours} * * *`;
    }else{
        cronExpression = `0 */${body.repeatTime} * * *`;
    }

    const timeZone = getTimeZonesFromOffset(body.TimezoneOffset);

    await agenda.cancel({ name: reminder.type });

    await agenda.every(cronExpression, reminder.type, {reminder:reminder._id},{timezone:timeZone});

    return res.status(200).json({ status: true, reminder })
});


const deleteNotification = catchAsync(async (req, res) => {

    const reminder = dailyReminderService.getDailyReminderById(req.params.id);
    if(!reminder){
        throw new ApiError(httpStatus.BAD_REQUEST,"Invalid Id");
    }
    await agenda.cancel({ name: reminder.type });

    await dailyReminderService.deleteDailyReminderById(req.params.id);

    return res.status(200).json({ status: true, msg: "Notification Deleted Succesfully" });
})

const  getTimeZonesFromOffset = (offsetMinute) => {
    const offsetMinutes = -offsetMinute
    const timeZones = [];
    moment.tz.names().forEach((tz) => {
        const zoneOffset = moment.tz(tz).utcOffset();

        console.log("zoneOffset",zoneOffset);
        console.log("offsetMinutes",offsetMinutes);
        console.log("tz",tz);

        // if(offsetMinutes < 0){
        //     pa
        // }

        if (zoneOffset === offsetMinutes) {
            timeZones.push(tz);
        }
    });

    console.log(timeZones);

    return timeZones[0];
}
  

module.exports = {
    list,
    sendNotification,
    deleteNotification,
    updateReminder,
    detail
};