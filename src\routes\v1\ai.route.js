const express = require("express");
const router = express.Router();
const { firebaseAuth } = require("../../middlewares/firebaseAuth");
const { aiController } = require("../../controllers");
const validate = require("../../middlewares/validate");
const { aiChatQuery } = require("../../validations/aiQuery.validation");

router.post("/workoutplan", firebaseAuth, aiController.getAiWorkoutPlan);
router.post("/dietplan", firebaseAuth, aiController.getAiDietPlan);
router.post("/casual-response", firebaseAuth, aiController.casualReply);
router.post("/suggestion", firebaseAuth, aiController.suggestionReply);
router.post("/duration", firebaseAuth, aiController.getDuration);

router.post("/chat", firebaseAuth, validate(aiChatQuery), aiController.chat);

router.post(
  "/queryResponse",
  firebaseAuth,
  // validate(aiChatQuery),
  aiController.handleQuery
);
router.get("/getfiles", aiController.getFiles);
router.delete("/deletefiles", aiController.deleteAllFile);
router.get("/vector-stores", aiController.getAllVectorStore);
router.delete("/vector-delete", aiController.deleteAllVectorStore);
// router.post("/processQuery", firebaseAuth, aiController.processVectorQuery); // this is for the vector store exisitng plans
router.get("/chatHistory", firebaseAuth, aiController.chatHistory); // this is for the vector store exisitng plans
router.get("/files", aiController.getFiles); // this is for the vector store exisitng plans
router.get("/store", aiController.getAllVectorStore); // this is for the vector store exisitng plans
router.delete("/store", aiController.deleteAllVectorStore); // this is for the vector store exisitng plans
router.delete("/files", aiController.deleteAllFile); // this is for the vector store exisitng plans

module.exports = router;
