const express = require('express');
const router = express.Router();

const dashboardController = require('../../../controllers/admin/dashboard.controller');
const { adminProtect } = require('../../../middlewares/adminAuth');

router.get(
    '/revenueGraph',
    adminProtect,
    dashboardController.revenueGraph
);

router.get(
    '/userStats',
    adminProtect,
    dashboardController.userStats
);

router.get(
    '/engagementGraph',
    adminProtect,
    dashboardController.engagementGraph
);

router.get(
    '/stats',
    adminProtect,
    dashboardController.stats
);

module.exports = router;




