const Joi = require('joi');
const {objectId} = require('./custom.validation');

const updateUser = {
  body: Joi.object().keys({
    name: Joi.string().trim(),
    phone: Joi.string().trim(),
    email: Joi.string().trim(),
  }),
};

const updateUserPreferences = {
  body: Joi.object().keys({
    fitnessGoals: Joi.array(),
    trainingDays:Joi.array(),
    activityLevel:Joi.string(),
    dietPreference:Joi.string()
  }),
};

const deleteUser = {
  params: Joi.object().keys({
    userId: Joi.string().custom(objectId),
  }),
};

module.exports = {
  updateUser,
  deleteUser,
  updateUserPreferences,
};
