const Joi = require('joi');
const { objectId } = require('./custom.validation');

const blogSchema = {
    title: Joi.string(),
    description: Joi.string(),
    body: Joi.string(),
    tags: Joi.array(),
};


const AddBlog = {
    body: Joi.object().keys({
        ...blogSchema,
    }),
};

const EditBlog = {
    body: Joi.object().keys({
        _id: Joi.string()
            .custom(objectId),
        ...blogSchema,
    }),
};

module.exports = {
    AddBlog,
    EditBlog
};
