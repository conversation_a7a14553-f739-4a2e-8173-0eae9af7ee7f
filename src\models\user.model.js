const mongoose = require("mongoose");
const { paginate } = require("./plugins/paginate");

const userSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      trim: true,
    },
    isBlocked: {
      type: Boolean,
      default: false,
    },
    phone: {
      type: String,
      trim: true,
      default: null,
    },
    email: {
      type: String,
      trim: true,
      // required: true,
    },
    profilePic: {
      type: {
        key: String,
        url: String,
      },
      default: null,
    },
    dob: {
      type: Date,
      default: null,
    },
    firebaseUid: {
      type: String,
      required: true,
      unique: true,
    },
    firebaseSignInProvider: {
      type: String,
      required: true,
    },
    gender: {
      type: String,
      enum: ["Male", "Female"],
    },
    fitnessGoals: [{ type: String }],
    weight: {
      type: Number,
    },
    weightUnit: {
      type: String,
      enum: ["KG", "POUND"],
      default: "KG",
    },
    height: {
      type: Number,
    },
    WIR: {
      interval: Number,
      startTime: String,
      endTime: String,
      isActive: {
        type: Boolean,
        default: true,
      },
    }, // water intake
    MoR: {
      interval: Number,
      startTime: String,
      endTime: String,
      isActive: {
        type: Boolean,
        default: true,
      },
    }, // movement reminder
    activityLevel: {
      type: String,
      enum: ["Beginner", "Normal", "Advanced", "Medium"],
    },
    dietPreference: {
      type: String,
    },
    trainingDays: [
      {
        day: String,
        active: Boolean,
      },
    ],
    stripeCustomerId: {
      type: String,
      default: null,
    },
    isBlocked: {
      type: Boolean,
      default: false,
    },
    isDeleted: {
      // to soft delete user. if(isDeleted = true), then user is deleted.
      type: Boolean,
      default: false,
    },
    preferences: {
      type: {
        notificationEnabled: Boolean,
        locationShared: Boolean,
      },
      default: {
        notificationEnabled: false,
        locationShared: false,
      },
    },
    referredBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    referralCode: {
      type: String,
      default: null,
    },
    // currentActivityLevel: [
    //   {
    //     type: String,
    //     enum: ["sedentary", "moderately active", "very active"],
    //   },
    // ],
    motivation: { type: String }, //To become confidence
    exerciseSessionDuration: { type: String }, // 40      ----duration in mins
    equipmentsAvaliable: [
      { type: mongoose.Schema.Types.ObjectId, ref: "Equipment" },
    ],

    exercisePreferred: [
      {
        type: String,
      },
    ],
    targetBodyPart: [{ type: String }], // hips
    trainingEnvironment: [
      {
        type: String,
        enum: ["home", "gym", "outdoor"],
      },
    ],
    allergies: [{ type: String }], // honey because of pollen
    dietaryRestrictions: [{ type: String }], //lactose intolerant
    medicalConditions: [{ type: String }], // eg - diabetes
    medications: [{ type: String }], // chlorocalciferol dv 60k

    virtualTrainerRequired: { type: Boolean },
    groceryBudget: { type: Number, min: 1 }, //weekly
    specificFitnesstargets: [{ type: String }], // marathon , lifiting 100kg
    mealType: [
      {
        type: String,
        // enum: ["low-carb", "high-protein", "balanced diet"],
      },
    ],
    threadId: {
      type: String,
      default: null,
    },
    assistantId: {
      type: String,
      default: null,
    },
  },

  { timestamps: true }
);

userSchema.plugin(paginate);

const User = mongoose.model("User", userSchema);

module.exports = {
  User,
};
