const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const catchAsync = require('../utils/catchAsync');
const { challengeService, userCoinWalletService, challengeParticipateService } = require('../services');
const { Challenge } = require('../models/challenge.model');
const { UserCoinWallet } = require('../models/userCoinWallet.model');
const { CoinWalletTransaction } = require('../models/coinWalletTransaction.model');
const notiFunc = require('../microservices/notification.service');
const UserNotification = require('../models/userNotification.model');

const participate = catchAsync(async (req, res) => {

    const user = req.user;
    const body = req.body;

    console.log(body)
    const challenge = await Challenge.findById(body.challenge);

    if(!challenge){
        throw new ApiError(httpStatus.BAD_REQUEST,"Invalid Challenge");
    }

    const wallet = await userCoinWalletService.createAndGetWallet(user._id);

    if(challenge.entryCoins  >  wallet.balance){
        throw new ApiError(httpStatus.BAD_REQUEST,"Insufficent Balance");
    }

    const challengeParticipate = await challengeParticipateService.createParticipate({...body,entryCoins:challenge.entryCoins,lastAttempAt:new Date(),user:user._id},req.file);


    const coinWalletTransaction = await CoinWalletTransaction.create({
        userId:user._id,
        walletId:wallet._id,
        transactionType:"debit",
        coins:challenge.entryCoins,
        balanceBefore:wallet.balance,
        balanceAfter:wallet.balance - challenge.entryCoins,
        description:"",
        challenge:challenge._id
      });

      wallet.balance = coinWalletTransaction.balanceAfter;
      await wallet.save();

      const userNotiData =  {
        receiver:user._id,
        // notification:user._id,
        title:"Thanks for participating",
        description:"Join our workout challenges to stay motivated, achieve your goals, and connect with a supportive fitness community. Get fit together!",
      }
      const userNotifications = await UserNotification.create(userNotiData);
      await notiFunc.sendToTopic(user._id.toString(), { title: userNotiData.title, body: userNotiData.description });
      res.status(200).json({
        message: "You have Participated to this challenge successfully",
        data: challengeParticipate,
        status:true
      })


});

const leaderboard = catchAsync(async (req, res) => {
    const challengeParticipates = await challengeParticipateService.getleaderBoard(req.params.id)
    res.status(200).json({
        message: "",
        data: challengeParticipates,
        status:true
    });
});


module.exports = {
    participate,
    leaderboard
}