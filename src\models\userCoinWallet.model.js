const mongoose = require('mongoose');
const { paginate } = require('./plugins/paginate');

const userCoinWalletSchema = new mongoose.Schema(
    {
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "User",
        },
        balance:{
            type:Number,
            default:0
        }
    },
    { timestamps: true }
);

userCoinWalletSchema.plugin(paginate);

const UserCoinWallet = mongoose.model('UserCoinWallet', userCoinWalletSchema);
module.exports = {
    UserCoinWallet
};
