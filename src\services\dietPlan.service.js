const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { MeasurementUnit, Ingredient, Recipe, DietPlan } = require('../models');
const { getAllData } = require('../utils/getAllData');
const { fileUploadService } = require('../microservices');

const addMeasurementUnit = async (measurementUnitData) => {
  try {
    const newMeasurementUnit = await MeasurementUnit.create(measurementUnitData);
    return newMeasurementUnit;
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error adding measurement unit');
  }
};

const updateMeasurementUnit = async (measurementUnitId, updateData) => {
  try {
    const updatedMeasurementUnit = await MeasurementUnit.findByIdAndUpdate(
      measurementUnitId,
      updateData,
      { new: true }
    );
    if (!updatedMeasurementUnit) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Measurement unit not found');
    }
    return updatedMeasurementUnit;
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error updating measurement unit');
  }
};

const listMeasurementUnit = async (keyword = '', timeFrame = 'all', options) => {
  try {
    let filter = {};
  
    if (keyword) {
      filter.$or = [
        { name: { $regex: keyword, $options: 'i' } },
        { fullForm: { $regex: keyword, $options: 'i' } }
      ];
    }

    // Filtering based on timeFrame
    if (timeFrame !== 'all') {
      const now = new Date();
      let startDate;
      switch (timeFrame) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'thisWeek':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay());
          break;
        case 'thisMonth':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'thisYear':
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
        default:
          startDate = new Date(0);
      }
      filter.createdAt = { $gte: startDate, $lte: now };
    }

    const measurementUnits = await MeasurementUnit.paginate(filter, options);;
    return measurementUnits;
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing measurement units');
  }
};

const addIngredient = async (ingredientData,image) => {
  try {

    let data = {...ingredientData};

    if (image) {
        const [thumbnail] = await fileUploadService.s3Upload([image], 'thumbnail').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload thumbnail');
        })
        data = { ...data, thumbnail};
    };

    const newIngredient = await Ingredient.create(data);
    return newIngredient;
  } catch (error) {
    console.log(error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error adding ingredient');
  }
};

const updateIngredient = async (ingredientId, updateData,image) => {
  try {

    const ingredient = await Ingredient.findById(ingredientId);
    let updates = { ...updateData };

    if (image) {
        const [thumbnail] = await fileUploadService.s3Upload([image], 'thumbnail').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload thumbnail');
        });
        if (ingredient.thumbnail) {
            const oldPicKey = ingredient.thumbnail.key;
            await fileUploadService
                .s3Delete(oldPicKey)
                .catch(err => console.log('Failed to delete thumbnail', oldPicKey));
        }
        updates = { ...updates, thumbnail };
    }
    const updatedIngredient = await Ingredient.findByIdAndUpdate(ingredientId, updates, { new: true });
    if (!updatedIngredient) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Ingredient not found');
    }
    return updatedIngredient;
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error updating ingredient');
  }
};

const listIngredients = async (keyword = '', timeFrame = 'all', options) => {
  try {
    let filter = {};
    // Filtering based on keyword
    if (keyword) {
      filter.name = { $regex: keyword, $options: 'i' };
    }

    // Filtering based on timeFrame
    if (timeFrame !== 'all') {
      const now = new Date();
      let startDate;
      switch (timeFrame) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'thisWeek':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay());
          break;
        case 'thisMonth':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'thisYear':
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
        default:
          startDate = new Date(0);
      }
      filter.createdAt = { $gte: startDate, $lte: now };
    }

    const ingredients = await Ingredient.paginate(filter, options);
    console.log(filter, options);
    return ingredients;
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing ingredients');
  }
};

const deleteIngredient = async (ingredientId) => {
  try {
    await Ingredient.findByIdAndDelete(ingredientId);
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error deleting ingredient');
  }
};

const addRecipe = async (recipeData) => {
  try {
    const newRecipe = await Recipe.create(recipeData);
    return newRecipe;
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error adding recipe');
  }
};

const updateRecipe = async (recipeId, updateData) => {
  try {
    const updatedRecipe = await Recipe.findByIdAndUpdate(recipeId, updateData, { new: true });
    if (!updatedRecipe) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Recipe not found');
    }
    return updatedRecipe;
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error updating recipe');
  }
};

const listRecipes = async (keyword = '', timeFrame = 'all', options) => {
  try {
    let filter = {};

    // Filtering based on keyword
    if (keyword) {
      filter.$or = [
        { name: { $regex: keyword, $options: 'i' } },
        { description: { $regex: keyword, $options: 'i' } }
      ];
    }

    // Filtering based on timeFrame
    if (timeFrame !== 'all') {
      const now = new Date();
      let startDate;
      switch (timeFrame) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'thisWeek':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay());
          break;
        case 'thisMonth':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'thisYear':
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
        default:
          startDate = new Date(0);
      }
      filter.createdAt = { $gte: startDate, $lte: now };
    }

    const recipes = await Recipe.paginate(filter, options);
    return recipes;
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing recipes');
  }
};

const deleteRecipe = async (recipeId) => {
  try {
    await DietPlan.updateMany(
      { "weeklySchedule.meals.recipes": recipeId }, // Filter documents containing the deleted location ID
      { $pull: { "weeklySchedule.$[].meals.$[meal].recipes": recipeId } }, // Pull the recipeId from the array
      { multi: true, arrayFilters: [{ "meal.recipes": recipeId }] }
    );

    await Recipe.findByIdAndDelete(recipeId);


    return true;
      

  } catch (error) {
    console.log(error)
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error deleting recipe');
  }
};

const deleteMeasurementUnit = async (recipeId) => {
  try {
    await MeasurementUnit.findByIdAndDelete(recipeId);
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error deleting Measurement Unit');
  }
};

const addDietPlan = async (dietPlanData) => {
  const newDietPlan = await DietPlan.create(dietPlanData);
  return newDietPlan;
};

const updateDietPlan = async ( _id, updatedData ) => {
  const updatedDietPlan = await DietPlan.findByIdAndUpdate(
    _id,
    updatedData,
    { new: true }
  );
  return updatedDietPlan;
};

const updateMediaForDietPlan = async ( _id, thumbnail ) => {
  const updatedDietPlan = await DietPlan.findByIdAndUpdate(
    _id,
    { thumbnail },
    { new: true }
  );
  return updatedDietPlan;
};

const listDietPlans = async (sortOrder = 'desc', keyword = '', timeFrame = 'all', page= 1, limit = 10) => {
  try {
    let filter = {};

    // Filtering based on keyword (if provided)
    if (keyword) {
      filter.$or = [
        { planTypes: { $regex: keyword, $options: 'i' } }, // Case-insensitive match for 'planType'
        { description: { $regex: keyword, $options: 'i' } }, // Case-insensitive match for 'description'
        { name: { $regex: keyword, $options: 'i' } } // Case-insensitive match for 'exerciseName'
        // Add more fields as needed for the keyword search
      ];
    }

    // Filtering based on timeFrame
    if (timeFrame !== 'all') {
      const now = new Date();
      let startDate;
      switch (timeFrame) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'thisWeek':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay());
          break;
        case 'thisMonth':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'thisYear':
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
        default:
          startDate = new Date(0); // To include all dates (past and present)
      }
      filter.createdAt = { $gte: startDate, $lte: now };
    }

    // Aggregate query
    const plansByGoal = await DietPlan.aggregate([
      { $match: filter }, // Match stage for keyword-based filtering
      {
        $unwind: '$goal'
      },
      {
        $group: {
          _id: { $toLower: '$goal' },
          count: { $sum: 1 },
          dietPlans: {
            $push: {
              dietPlanId: '$_id',
              thumbnail: '$thumbnail',
              planTypes: '$planTypes',
              description: '$description',
              name: '$name'
            }
          }
        }
      },
      {
        $sort: { count: sortOrder === 'asc' ? 1 : -1 } // Sort based on sortOrder
      }
    ]);

    return plansByGoal;
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing diet plans');
  }
};

const deleteDietPlan = async (recipeId) => {
  try {
    await DietPlan.findByIdAndDelete(recipeId);
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error deleting recipe');
  }
};

const addIngredientsToRecipe = async (recipeId, ingredients) => {
  try {
    const recipe = await Recipe.findById(recipeId);
    if (!recipe) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Recipe not found');
    }
  
    recipe.ingredients.push(...ingredients);
    return await recipe.save();
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error adding ingredients to recipe');
  }
};

const updateIngredientsInRecipe = async (recipeId, updatedIngredients) => {

  try {
    const recipe = await Recipe.findById(recipeId);
    if (!recipe) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Recipe not found');
    }

    // Assign updated ingredients directly to recipe
    recipe.ingredients = updatedIngredients;

    return await recipe.save();
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error updating ingredients in recipe');
  }
};


const deleteIngredientFromRecipe = async (recipeId, ingredientName) => {
  try {
    const recipe = await Recipe.findById(recipeId);
    if (!recipe) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Recipe not found');
    }
  
    recipe.ingredients = recipe.ingredients.filter((ingredient) => ingredient.name !== ingredientName);
    return await recipe.save();
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error deleting ingredient from recipe');
  }
};

const updateNutritionalValues = async (recipeId, nutritionalValues) => {
  try {
    const recipe = await Recipe.findById(recipeId);
    if (!recipe) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Recipe not found');
    }
  
    Object.keys(nutritionalValues).forEach((key) => {
      const value = nutritionalValues[key];
      recipe.nutritionalValue[key] = value;
    });
  
    return await recipe.save();
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error updating nutritional values in recipe');
  }
};

const reshuffleRecipes = async (dietPlanId, day, mealType, recipeId, newPosition) => {
  try {
    const dietPlan = await DietPlan.findById(dietPlanId);
    if (!dietPlan) {
      throw new Error('Diet plan not found');
    }

    const selectedDay = dietPlan.weeklySchedule.find(schedule => schedule.day === day);
    if (!selectedDay) {
      throw new Error('Selected day not found');
    }

    const selectedMeal = selectedDay.meals.find(meal => meal.mealType === mealType);
    if (!selectedMeal) {
      throw new Error('Meal type not found on the selected day');
    }

    const recipeIndex = selectedMeal.recipes.findIndex(id => id.toString() === recipeId);
    if (recipeIndex === -1) {
      throw new Error('Recipe not found in the selected meal');
    }

    const [removedRecipe] = selectedMeal.recipes.splice(recipeIndex, 1);
    selectedMeal.recipes.splice(newPosition - 1, 0, removedRecipe);
    
    return await dietPlan.save();
  } catch (error) {
    throw new Error(`Error reshuffling recipes: ${error.message}`);
  }
};

const addRecipesToMeal = async (dietPlanId, day, mealType, recipeIds) => {
  try {
    let dietPlan = await DietPlan.findById(dietPlanId);
    if (!dietPlan) {
      throw new Error('Diet plan not found');
    }

    let selectedDay = dietPlan.weeklySchedule.find(schedule => schedule.day === day);
    if (!selectedDay) {
      // Create a new day if it doesn't exist
      selectedDay = {
        day: day,
        meals: []
      };
      dietPlan.weeklySchedule.push(selectedDay);
    }

    let selectedMeal = selectedDay.meals.find(meal => meal.mealType === mealType);
    if (!selectedMeal) {
      // Create a new meal if it doesn't exist for the selected day
      selectedMeal = {
        mealType: mealType,
        recipes: recipeIds // Assign the recipeIds directly if the meal doesn't exist
      };
      selectedDay.meals.push(selectedMeal);
    } else {
      // Check and add new recipes to the existing meal
      for (const recipeId of recipeIds) {
        const existingRecipeIndex = selectedMeal.recipes.findIndex(id => id.toString() === recipeId);
        if (existingRecipeIndex === -1) {
          selectedMeal.recipes.push(recipeId);
        }
      }
    }

    // Update the DietPlan
    dietPlan = await dietPlan.save();
    return dietPlan;
  } catch (error) {
    throw new Error(`Error adding recipes to the meal: ${error.message}`);
  }
};


const removeRecipeFromMeal = async (dietPlanId, day, mealType, recipeId) => {
  try {
    const dietPlan = await DietPlan.findById(dietPlanId);
    if (!dietPlan) {
      throw new Error('Diet plan not found');
    }

    const selectedDay = dietPlan.weeklySchedule.find(schedule => schedule.day === day);
    if (!selectedDay) {
      throw new Error('Selected day not found');
    }

    const selectedMeal = selectedDay.meals.find(meal => meal.mealType === mealType);
    if (!selectedMeal) {
      throw new Error('Meal type not found on the selected day');
    }

    const indexToRemove = selectedMeal.recipes.findIndex(id => id.toString() === recipeId);
    if (indexToRemove === -1) {
      throw new Error('Recipe not found in the selected meal');
    }

    selectedMeal.recipes.splice(indexToRemove, 1);
    
    return await dietPlan.save();
  } catch (error) {
    throw new Error(`Error removing recipe from the meal: ${error.message}`);
  }
};

const viewDietPlan = async (dietPlanId,day) => {
  try {
    const dietPlan = await DietPlan.findById(dietPlanId).populate("weeklySchedule.meals.recipes");

    if (!dietPlan) {
      throw new Error('Diet plan not found');
    }
    const rearrangedDietPlan = rearrangeDietPlanData(dietPlan,day);
    return rearrangedDietPlan;
  } catch (error) {
    throw new Error(`Error fetching diet plan: ${error.message}`);
  }
};

const rearrangeDietPlanData = (dietPlan,day) => {
  const daysOrder = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  const mealTypesOrder = ['Breakfast', 'Lunch', 'Snacks', 'Dinner'];

  let rearrangedData;

  if(day){
    const sortedDaySchedule  = dietPlan.weeklySchedule.find(schedule => schedule.day == day);

    if(sortedDaySchedule){
      sortedDaySchedule.meals
      .sort((mealA, mealB) => mealTypesOrder.indexOf(mealA.mealType) - mealTypesOrder.indexOf(mealB.mealType))
      .filter((sortedDaySchedule) => mealTypesOrder.includes(sortedDaySchedule.mealType))
    }

    rearrangedData = {
      thumbnail: dietPlan.thumbnail,
      planTypes: dietPlan.planTypes,
      _id:dietPlan._id,
      goal: dietPlan.goal,
      name: dietPlan.name,
      description: dietPlan.description,
      // weeklySchedule: sortedWeeklySchedule,
      daySchedule: sortedDaySchedule|| null,
    };
  
  }else{

    const sortedWeeklySchedule = (dietPlan.weeklySchedule || [])
    .sort((a, b) => daysOrder.indexOf(a.day) - daysOrder.indexOf(b.day))
    .map((originalDay) => ({
      day: originalDay.day,
      meals: originalDay.meals
        .sort((mealA, mealB) => mealTypesOrder.indexOf(mealA.mealType) - mealTypesOrder.indexOf(mealB.mealType))
        .filter((originalMeal) => mealTypesOrder.includes(originalMeal.mealType))
    }));
      rearrangedData = {
        thumbnail: dietPlan.thumbnail,
        planTypes: dietPlan.planTypes,
        goal: dietPlan.goal,
        _id:dietPlan._id,
        name: dietPlan.name,
        description: dietPlan.description,
        weeklySchedule: sortedWeeklySchedule,
      };
  }
  

  


  // Include additional fields from the original diet plan
 
  return rearrangedData;
};



const viewRecipe = async (recipeId) => {
  try {
    const dietPlan = await Recipe.findById(recipeId);

    if (!dietPlan) {
      throw new Error('Recipe not found');
    }

    return dietPlan;
  } catch (error) {
    throw new Error(`Error fetching Recipe: ${error.message}`);
  }
};

const viewIngredient = async (ingredientId) => {
  try {
    const dietPlan = await Ingredient.findById(ingredientId);

    if (!dietPlan) {
      throw new Error('Ingredient not found');
    }

    return dietPlan;
  } catch (error) {
    throw new Error(`Error fetching Ingredient: ${error.message}`);
  }
};


async function getAllDietPlanWithCustomFilters(filters, options) {
  const data =  await DietPlan.paginate(filters,options); //await getAllData(DietPlan, query, populateConfig)
  return data;
}


module.exports = {
  addMeasurementUnit,
  updateMeasurementUnit,
  listMeasurementUnit,
  addIngredient,
  updateIngredient,
  listIngredients,
  deleteIngredient,
  addRecipe,
  updateRecipe,
  listRecipes,
  deleteRecipe,
  addDietPlan,
  updateDietPlan,
  listDietPlans,
  deleteDietPlan,
  addIngredientsToRecipe,
  updateIngredientsInRecipe,
  deleteIngredientFromRecipe,
  updateNutritionalValues,
  reshuffleRecipes,
  addRecipesToMeal,
  removeRecipeFromMeal,
  viewDietPlan,
  viewRecipe,
  deleteMeasurementUnit,
  updateMediaForDietPlan,
  viewIngredient,
  getAllDietPlanWithCustomFilters
};
