const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const catchAsync = require('../utils/catchAsync');
const {equipmentService} = require('../services');

const getAllEquipments = catchAsync(async (req, res) => {
    const equipments = await equipmentService.getAllEquipments(req.query,[])
    res.status(200).json({
        message:"All equipments",
        data:equipments
    })
});

const getEquipmentById = catchAsync(async (req, res) => {
    const equipment = await equipmentService.getEquipmentById(req.params.id)
    res.status(200).json({
        message:"All equipments",
        data:equipment
    })
});


module.exports = {
    getAllEquipments,
    getEquipmentById
}