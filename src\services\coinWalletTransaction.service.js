const userCoinWalletService = require('./userCoinWallet.service');
const config = require('../config/config');
const { CoinWalletTransaction } = require('../models/coinWalletTransaction.model');


async function create(userId,type,coins,coinOffer = null,description = "",paymentId=null) {
  
    
    const wallet = await userCoinWalletService.createAndGetWallet(userId);

    let balanceAfter = 0;
    if(type ==  "credit"){
        balanceAfter = wallet.balance + coins;
    }else{
        balanceAfter = wallet.balance - coins;
    }

    const coinWalletTransaction = await CoinWalletTransaction.create({
        userId:userId,
        walletId:wallet._id,
        transactionType:type,
        coinOffer:coinOffer,
        coins:coins,
        balanceBefore:wallet.balance,
        balanceAfter:balanceAfter,
        description:description,
        paymentId:paymentId,
    });

    wallet.balance = coinWalletTransaction.balanceAfter;
    await wallet.save();

    return coinWalletTransaction;
}




module.exports = {
    create
};