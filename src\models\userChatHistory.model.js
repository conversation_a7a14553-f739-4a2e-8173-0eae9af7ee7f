const mongoose = require("mongoose");

const userChatHistorySchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  chat: [
    {
      userQuery: {
        type: String,
        required: true,
      },
      answer: {
        type: String,
        required: true,
      },
      timestamp: {
        type: Date,
        default: Date.now,
      },
    },
  ],
});

const UserChatHistory = mongoose.model(
  "UserChatHistory",
  userChatHistorySchema
);

module.exports = UserChatHistory;
