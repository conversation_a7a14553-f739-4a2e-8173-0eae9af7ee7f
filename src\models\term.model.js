const mongoose = require('mongoose');
const { paginate } = require('./plugins/paginate');

const termSchema = new mongoose.Schema(
    {
        title: {
            type: String,
            default: null,
        },
        body: {
            type: String,
            default: null,
        }
    },
    { timestamps: true }
);

termSchema.plugin(paginate);

const Term = mongoose.model('Term', termSchema);
module.exports = {
    Term
};
