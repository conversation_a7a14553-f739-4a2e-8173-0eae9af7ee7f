const Joi = require('joi');
const { objectId, validateMediaExtension, validateMaxWords } = require('./custom.validation');


const measurementUnitSchema = {
    name:Joi.string().trim(),
    fullForm:Joi.string().trim()
};

const ingredientSchema = {
  name: Joi.string(),
  type: Joi.string(),
  calories: Joi.number(),
  measurementQuantity: Joi.number(),
  measurementUnit: Joi.string()
};

const ingredientRecipeSchema = {
  name: Joi.string(),
  measurement: Joi.string(),
  calories:Joi.string().empty(),
  thumbnail:Joi.string().empty(),
};

const nutritionalValueSchema = {
  Protein: Joi.number(),
  Carbs: Joi.number(),
  Kcal: Joi.number(),
  fiber: Joi.number(),
  Sugar: Joi.number(),
  fat: Joi.number(),
  saturatedFat: Joi.number(),
  unsaturatedFat: Joi.number()
};

const recipeSchema ={
  planTypes: Joi.array(),
  calories: Joi.number(),
  name: Joi.string(),
  description: Joi.string(),
  instructions: Joi.string().custom((value, helpers) => {
    return validateMaxWords(value, helpers, 50);
  }, 'Instructions Validation'),
  ingredients: Joi.array().items(ingredientRecipeSchema),
  nutritionalValue: nutritionalValueSchema
};


const addMeasurementUnit = {
  body: Joi.object().keys({
    ...measurementUnitSchema,
  }),
};

const updateMediaForDietPlan = {
  body: Joi.object().keys({
    id: Joi.string().custom(objectId),
    thumbnail: Joi.string().custom((value, helpers) => {
      return validateMediaExtension(value, helpers, 'image');
    }, 'Thumbnail Validation'),
  }),
};

const updateMediaForRecipe = {
  body: Joi.object().keys({
    id: Joi.string().custom(objectId),
    thumbnail: Joi.string().custom((value, helpers) => {
      return validateMediaExtension(value, helpers, 'image');
    }, 'Thumbnail Validation'),
    recipeVideo: Joi.string().custom((value, helpers) => {
      return validateMediaExtension(value, helpers, 'video');
    }, 'Recipe Video Validation')
  }),
};

const updateMeasurementUnit = {
    body: Joi.object().keys({
        id: Joi.string().custom(objectId),
        ...measurementUnitSchema,
    }),
  };
  
const addIngredient = {
    body: Joi.object().keys({
        ...ingredientSchema,
    }),
  };

const updateIngredient = {
    body: Joi.object().keys({
        id: Joi.string().custom(objectId),
        ...ingredientSchema,
    }),
  };

const addRecipe = {
    body: Joi.object().keys({
        ...recipeSchema,
    }),
  };

const updateRecipe = {
    body: Joi.object().keys({
        id: Joi.string().custom(objectId),
        ...recipeSchema,
    }),
  };

  const addDietPlan = {
  body: Joi.object({
    planTypes: Joi.array().required(),
    goal: Joi.array().items(Joi.string()),
    name: Joi.string(),
    description: Joi.string(),
    weeklySchedule: Joi.array().items(Joi.object({
      day: Joi.string().valid('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'),
      meals: Joi.array().items(Joi.object({
        mealType: Joi.string().valid('Breakfast', 'Lunch', 'Snacks', 'Dinner'),
        recipes: Joi.array().items(Joi.string().custom(objectId)),
      }))
    }))
  })
};
  
  const updateDietPlan = {
    body: Joi.object({
    id: Joi.string().custom(objectId),
    planTypes: Joi.array(),
    goal: Joi.array().items(Joi.string()),
    name: Joi.string(),
    description: Joi.string(),
    weeklySchedule: Joi.array().items(Joi.object({
      day: Joi.string().valid('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'),
      meals: Joi.array().items(Joi.object({
        mealType: Joi.string().valid('Breakfast', 'Lunch', 'Snacks', 'Dinner'),
        recipes: Joi.array().items(Joi.string().custom(objectId)),
      }))
    }))
  })
};

module.exports = {
  addMeasurementUnit,
  updateMeasurementUnit,
  addIngredient,
  updateIngredient,
  addRecipe,
  updateRecipe,
  addDietPlan,
  updateDietPlan,
  updateMediaForRecipe,
  updateMediaForDietPlan
};
