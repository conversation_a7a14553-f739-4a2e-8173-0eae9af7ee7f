const mongoose = require('mongoose');
const { paginate } = require('./plugins/paginate');

const equipmentSchema = new mongoose.Schema(
    {
        thumbnail: {
            type: {
                key: String,
                url: String,
            },
            default: null,
        },
        name: {
            type: String,
            default: null,
        }
    },
    { timestamps: true }
)

equipmentSchema.plugin(paginate);

const Equipment = mongoose.model('Equipment', equipmentSchema);
module.exports = {
    Equipment
};