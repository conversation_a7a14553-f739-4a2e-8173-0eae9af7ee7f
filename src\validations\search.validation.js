const Joi = require('joi');
const { validateSpecial<PERSON>har } = require('./custom.validation');



const query = {
    query: Joi.object().keys({
      search: Joi.string().optional().custom(validateSpecialChar),
      keyword: Joi.string().optional().custom(validateSpecialChar),
      timeFrame: Joi.string().optional(),
      sortOrder: Joi.string().optional(),
      limit:Joi.number().optional(),
      page:Joi.number().optional()
    }),
};

module.exports = {
  query,
};