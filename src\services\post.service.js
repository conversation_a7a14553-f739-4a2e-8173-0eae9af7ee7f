const { User, Client } = require('../models');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { fileUploadService } = require('../microservices');
const { Post } = require('../models/post.model');


async function getPostById(id) {
    const post = await Post.findById(id).populate("userId","_id name profilePic");
    return post;
}

async function getPosts(filter, options) {

    options.populate = [
        "userId::_id,name,profilePic"
    ];
    return await Post.paginate(filter, options);

}

async function createPost(details, image) {
    let data = { ...details };

    if (image) {
        const [thumbnail] = await fileUploadService.s3Upload([image], 'thumbnail').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload thumbnail');
        })
        data = { ...data, image: thumbnail };
    };

    return await Post.create(data);
}

async function updatePostById(id, body, image) {
    const post = await Post.findById(id);
    let updates = { ...body };

    if (image) {
        const [thumbnail] = await fileUploadService.s3Upload([image], 'thumbnail').catch(err => {
            throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload thumbnail');
        });
        if (post.thumbnail) {
            const oldPicKey = post.thumbnail.key;
            await fileUploadService
                .s3Delete(oldPicKey)
                .catch(err => console.log('Failed to delete image', oldPicKey));
        }
        updates = { ...updates, image: thumbnail };
    }

    return await Post.findByIdAndUpdate(id, updates, { new: true });
}

async function deletePostById(id) {
    try {
        await Post.findByIdAndDelete(id);
        return true;
    } catch (err) {
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to delete the Blog');
    }
}

module.exports = {
    getPosts,
    getPostById,
    createPost,
    updatePostById,
    deletePostById
}