const mongoose = require('mongoose');
const { paginate } = require("./plugins/paginate");
const timestampPlugin = require("./plugins/timestampPlugin");

// Ingredient Schema
const ingredientSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  measurement: { //1 gm
    type: String,
    required: true
  },
  calories: { //1 gm
    type: String,
    required: true
  },
  thumbnail:{
    type: String,
    required: true
  }
});

// Nutritional Value Schema
const nutritionalValueSchema = new mongoose.Schema({
  Protein: Number,
  Carbs: Number,
  Kcal: Number,
  fiber: Number,
  Sugar: Number,
  fat: Number,
  saturatedFat: Number,
  unsaturatedFat: Number,
});

// Recipe Schema
const recipeSchema = new mongoose.Schema({
  thumbnail: {
    type: String
  },
  planTypes: [{
    type: String
  }],
  calories: {
    type: Number
  },
  name: {
    type: String,
    required: true
  },
  description: {
    type: String
  },
  recipeVideo: {
    type: String
  },
  instructions: {
    type: String
  },
  ingredients: [ingredientSchema],
  nutritionalValue: nutritionalValueSchema
});

recipeSchema.plugin(paginate);
recipeSchema.plugin(timestampPlugin);

module.exports.Recipe = mongoose.model("Recipe",recipeSchema);
