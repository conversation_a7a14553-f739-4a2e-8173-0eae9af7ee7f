const express = require('express');
const router = express.Router();

const validate = require('../../../middlewares/validate');
const {adminProtect} = require('../../../middlewares/adminAuth');
const dietPlanValidation = require('../../../validations/dietPlan.validation');
const searchValidation = require('../../../validations/search.validation');

const dietPlanController = require('../../../controllers/admin/dietPlan.controller');
const { fileUploadService } = require('../../../microservices');

const uploadRecipefiles = fileUploadService.multerUpload.fields([
    { name: 'thumbnail', maxCount: 1 },
    { name: 'recipeVideo', maxCount: 1 },
  ]);


router.post(
  '/addMeasurementUnit',
  adminProtect,
  validate(dietPlanValidation.addMeasurementUnit),
  dietPlanController.addMeasurementUnit
);

router.post(
    '/updateMeasurementUnit',
    adminProtect,
    validate(dietPlanValidation.updateMeasurementUnit),
    dietPlanController.updateMeasurementUnit
);

router.get(
    '/listMeasurementUnit',
    adminProtect,
    validate(searchValidation.query),
    dietPlanController.listMeasurementUnit
);

router.post(
    '/addIngredient',
    adminProtect,
    fileUploadService.multerUpload.single('thumbnail'),
    validate(dietPlanValidation.addIngredient),
    dietPlanController.addIngredient
  );
  
  router.post(
    '/updateIngredient',
    adminProtect,
    fileUploadService.multerUpload.single('thumbnail'),
    validate(dietPlanValidation.updateIngredient),
    dietPlanController.updateIngredient
  );
  
  router.get(
    '/listIngredients',
    adminProtect,
    validate(searchValidation.query),
    dietPlanController.listIngredients
  );
  
  router.delete(
    '/deleteIngredient/:id',
    adminProtect,
    dietPlanController.deleteIngredient
  );

  router.post(
    '/addRecipe',
    adminProtect,
    validate(dietPlanValidation.addRecipe),
    dietPlanController.addRecipe
  );
  
  router.post(
    '/updateRecipe',
    adminProtect,
    validate(dietPlanValidation.updateRecipe),
    dietPlanController.updateRecipe
  );

  router.post(
    '/updateMediaForRecipe',
    adminProtect,
    uploadRecipefiles,
    validate(dietPlanValidation.updateMediaForRecipe),
    dietPlanController.updateMediaForRecipe
  );
  
  router.get(
    '/listRecipes',
    adminProtect,
    validate(searchValidation.query),
    dietPlanController.listRecipes
  );
  
  router.delete(
    '/deleteRecipe/:id',
    adminProtect,
    dietPlanController.deleteRecipe
  );

  router.post(
    '/addDietPlan',
    adminProtect,
    validate(dietPlanValidation.addDietPlan),
    dietPlanController.addDietPlan
  );
  
  router.post(
    '/updateDietPlan/:id',
    adminProtect,
    validate(dietPlanValidation.updateDietPlan),
    dietPlanController.updateDietPlan
  );

  router.post(
    '/updateMediaForDietPlan',
    adminProtect,
    fileUploadService.multerUpload.single('thumbnail'),
    validate(dietPlanValidation.updateMediaForDietPlan),
    dietPlanController.updateMediaForDietPlan
  );

  router.get(
    '/listDietPlans',
    adminProtect,
    validate(searchValidation.query),
    dietPlanController.listDietPlans
  );
  
  router.delete(
    '/deleteDietPlan/:id',
    adminProtect,
    dietPlanController.deleteDietPlan
  );

  router.delete(
    '/deleteMeasurementUnit/:id',
    adminProtect,
    dietPlanController.deleteMeasurementUnit
  );

  router.get(
    '/viewDietPlan/:dietPlanId',
    adminProtect,
    dietPlanController.viewDietPlan
  );

  router.get(
    '/viewRecipe/:recipeId',
    adminProtect,
    dietPlanController.viewRecipe
  );

  router.get(
    '/viewIngredient/:ingredientId',
    adminProtect,
    dietPlanController.viewIngredient
  );

  router.post(
    '/addIngredientsToRecipe',
    adminProtect,
    dietPlanController.addIngredientsToRecipe
  );

  router.post(
    '/updateIngredientInRecipe',
    adminProtect,
    dietPlanController.updateIngredientInRecipe
  );

  router.get(
    '/deleteIngredientFromRecipe',
    adminProtect,
    dietPlanController.deleteIngredientFromRecipe
  );

  router.post(
    '/updateNutritionalValues',
    adminProtect,
    dietPlanController.updateNutritionalValues
  );

  router.post(
    '/reshuffleRecipes',
    adminProtect,
    dietPlanController.reshuffleRecipes
  );

  router.post(
    '/addRecipeToMeal',
    adminProtect,
    dietPlanController.addRecipeToMeal
  );

  router.post(
    '/removeRecipeFromMeal',
    adminProtect,
    dietPlanController.removeRecipeFromMeal
  );
module.exports = router;
