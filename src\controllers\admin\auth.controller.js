const { Admin } = require("../../models/admin.model");
const catchAsync = require("../../utils/catchAsync");
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const config = require('../../config/config');
const ApiError = require("../../utils/ApiError");
const httpStatus = require("http-status");

const {secret ,expires_in} = config.jwt;

const signToken = id =>{
    return jwt.sign({id},secret,{expiresIn:expires_in});
}
const loginAdmin = catchAsync(async (req, res) => {
    const {email,password} = req.body;
    console.log(req);
    const admin = await Admin.findOne({email:email});
    if(!admin){
        return res.status(400).json({
            status:false,
            msg:"Incorrect username or password."
        })
    }
    
    const isCorrect = await bcrypt.compare(password,admin.password);
    if(isCorrect){
        let token = signToken(admin._id);
        admin.password = undefined
        return res.status(200).json({
            status:true,
            token,
            admin
        })
    }else {
        return res.status(400).json({
            status:false,
            msg:"Incorrect username or password."
        })
    }
    
});

const updatePassword = catchAsync(async (req, res) => {
    const user = req.admin;

    const {oldPassword , newPassword, confirmPassword} = req.body;

    const checkPassword = await bcrypt.compare(oldPassword, user.password);

    if(!checkPassword){
        throw new ApiError(httpStatus.BAD_REQUEST, 'Worng Old Password');
    }   

    if(newPassword !== confirmPassword){
        throw new ApiError(httpStatus.BAD_REQUEST, "New password dosn't confirm");
    }
    const newPasswordHash = await bcrypt.hash(newPassword,12);

   
    const admin = await Admin.findByIdAndUpdate(user._id,{password:newPasswordHash},{new:true});

    return res.status(200).json({
        status:true,
        data:admin
    });

})

const getDetails = catchAsync(async (req, res) => {
    const admin = req.admin;

    return res.status(200).json({
        status:true,
        data:admin
    });

})


module.exports = {
    loginAdmin,
    updatePassword,
    getDetails
}