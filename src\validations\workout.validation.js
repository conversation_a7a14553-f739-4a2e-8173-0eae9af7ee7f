const Joi = require('joi');
const { objectId } = require('./custom.validation');

const WorkoutSchema = {
    category:Joi.string()
        .trim()
        .required(),
    name:Joi.string()
        .trim()
        .required(),
    description:Joi.string()
        .trim()
        .required()
};



const AddWorkout = {
  body: Joi.object().keys({
    ...WorkoutSchema,
  }),
};

const EditWorkout = {
    body: Joi.object().keys({
        _id: Joi.string()
        .custom(objectId),
        ...WorkoutSchema,
    }),
  };

module.exports = {
    AddWorkout,
    EditWorkout
};
