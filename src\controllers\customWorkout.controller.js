const { CustomWorkoutPlan } = require("../models/customWorkoutModel");
const ApiError = require("../utils/ApiError");
const httpStatus = require("http-status");
const moment = require("moment");
const catchAsync = require("../utils/catchAsync");
const agenda = require("../config/agenda");
const { Exercise } = require("../models/exercise.model");
const { generateDescription } = require("../utils/helper");

const { processWorkoutPlan } = require("../utils/customPlanHandler");

const {
  uploadFileToVectorStore,
} = require("../aiServices/vectorStore.service");
const { vectorStoreService } = require("../aiServices");
const { CustomDietPlan } = require("../models/customDietPlanModel");

const createWorkoutPlan = catchAsync(async (req, res) => {
  const { startDate, leaveDay, workoutPlan, workoutTime } = req.body;
  const user = req.user;

  if (!startDate || !leaveDay || !workoutPlan || workoutPlan.length === 0) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Start date, leave day, or workout plan is missing"
    );
  }

  const leaveDayLower = leaveDay.toLowerCase();
  const startMoment = moment(startDate, "DD-MM-YYYY");

  if (!startMoment.isValid()) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Invalid start date format");
  }

  const existingPlan = await CustomWorkoutPlan.findOne({ user: user._id });

  // Remove any existing plan
  await CustomWorkoutPlan.deleteMany({ user: user._id });

  // Collect all unique exercise IDs
  const exerciseIds = Array.from(
    new Set(
      workoutPlan.flatMap((day) =>
        day.sections.flatMap((section) => section.exercises.map((ex) => ex._id))
      )
    )
  );

  // Fetch exercise metadata
  const exerciseDataMap = await Exercise.find({ _id: { $in: exerciseIds } })
    .lean()
    .then((exercises) =>
      exercises.reduce((acc, ex) => {
        acc[ex._id.toString()] = ex;
        return acc;
      }, {})
    );

  const muscleGroups = Object.values(exerciseDataMap).flatMap(
    (ex) => ex.musclegroupsinvolved || []
  );

  let currentMoment = startMoment.clone();
  let workoutIndex = 0;
  const formattedWorkoutPlan = [];

  while (workoutIndex < workoutPlan.length) {
    const currentDay = currentMoment.format("dddd").toLowerCase();

    if (currentDay !== leaveDayLower) {
      const dayPlan = workoutPlan[workoutIndex];

      const processedSections = dayPlan.sections.map((section) => ({
        section: section.section,
        exercises: section.exercises.map((ex) => ({
          _id: ex._id,
          name: ex.name,
          equipment: ex.equipment,
          weight: ex.weight,
          sets: ex.sets,
          reps: ex.reps,
          thumbnail:
            typeof ex.thumbnail === "object" && ex.thumbnail !== null
              ? ex.thumbnail.url
              : ex.thumbnail,
          video:
            typeof ex.video === "object" && ex.video !== null
              ? ex.video.url
              : ex.video,
          caloriesBurnt: ex.calorieBurned || null,
          duration: ex.duration || 0,
        })),
      }));

      formattedWorkoutPlan.push({
        date: currentMoment.format("YYYY-MM-DD"),
        description: dayPlan.description || "", // fallback if not present
        sections: processedSections,
      });

      workoutIndex++;
    }

    currentMoment.add(1, "day");
  }

  // Save plan to DB
  const customWorkoutPlan = new CustomWorkoutPlan({
    user: user._id,
    workoutPlan: formattedWorkoutPlan,
    notificationSchedule: { enabled: true, time: workoutTime },
  });

  const result = await customWorkoutPlan.save();

  const jsonlData = await processWorkoutPlan(
    formattedWorkoutPlan,
    user._id,
    user.name
  );

  if (jsonlData) {
    const uploadResult = await uploadFileToVectorStore(
      jsonlData,
      user._id,
      user.name,
      "workout",
      existingPlan
    );

    if (uploadResult) {
      const updatedPlan = await CustomWorkoutPlan.findOneAndUpdate(
        { user: user._id },
        { vectorFileId: uploadResult.fileId },
        { new: true }
      );

      if (updatedPlan) {
        const vectorStore = await vectorStoreService.getOrCreateVectorStore();
        const dietPlan = await CustomDietPlan.findOne({
          user: user._id,
        }).select("vectorFileId");
        const dietPlanFileId = dietPlan?.vectorFileId || "not available";

        const instructions = vectorStoreService.createInstructions(
          user,
          uploadResult.fileId,
          dietPlanFileId
        );

        await vectorStoreService.updateAssistant(
          user.assistantId,
          vectorStore.vectorStoreId,
          instructions,
          user._id
        );

        console.log("✅ Assistant updated with new workout plan.");
      }
    }
  }

  // Schedule first notification
  await agenda.cancel({
    name: "sendWorkoutNotification",
    "data.userId": user._id,
  });

  if (formattedWorkoutPlan.length > 0) {
    const firstWorkout = formattedWorkoutPlan[0];
    const [hour, minute] = workoutTime.split(":").map(Number);

    const notificationTime = moment(firstWorkout.date)
      .set({ hour, minute, second: 0 })
      .subtract(15, "minutes");

    console.log(
      `📅 Scheduling first workout notification for ${notificationTime.format()}`
    );

    await agenda.schedule(
      notificationTime.toDate(),
      "sendWorkoutNotification",
      {
        userId: user._id,
        workoutDetails: firstWorkout,
      }
    );
  }

  res.status(httpStatus.CREATED).send({
    message: "Workout plan created successfully",
    data: result,
  });
});

const getCustomWorkoutPlan = catchAsync(async (req, res) => {
  const userId = req.user._id;
  const { date } = req.query;

  console.log(`[INFO] User ID: ${userId}`);
  console.log(`[INFO] Date Query: ${date}`);

  const customWorkoutPlan = await CustomWorkoutPlan.findOne({
    user: userId,
  }).lean();

  console.log(`[DEBUG] Retrieved Workout Plan:`, customWorkoutPlan);

  if (!customWorkoutPlan) {
    console.error(`[ERROR] No workout plan found for user ID: ${userId}`);
    return res
      .status(httpStatus.NOT_FOUND)
      .send({ message: "No workout plan found" });
  }

  // If no date provided, return the full workout plan
  if (!date) {
    console.log(`[INFO] No date provided. Returning full workout plan.`);
    return res.status(httpStatus.OK).send({
      message: "Full workout plan fetched successfully",
      data: customWorkoutPlan.workoutPlan,
    });
  }

  // Validate date format
  if (!moment(date, "DD-MM-YYYY", true).isValid()) {
    console.error(`[ERROR] Invalid date format: ${date}`);
    return res
      .status(httpStatus.BAD_REQUEST)
      .send({ message: "Invalid date format. Please use DD-MM-YYYY" });
  }

  const formattedDate = moment(date, "DD-MM-YYYY").format("YYYY-MM-DD");

  console.log(`[INFO] Formatted Date: ${formattedDate}`);

  // Find specific workout for the date
  const filteredPlan = customWorkoutPlan.workoutPlan.find(
    (entry) => entry.date === formattedDate
  );

  if (!filteredPlan) {
    console.warn(`[WARN] No workout plan found for date: ${formattedDate}`);
    return res.status(httpStatus.OK).send({
      message: `No workout plan found for ${date}`,
      data: null,
    });
  }

  if (filteredPlan.isLeave) {
    console.log(`[INFO] ${date} is marked as a leave day.`);
    return res.status(httpStatus.OK).send({
      message: `${date} is a leave day`,
      data: filteredPlan,
    });
  }

  // Dynamically calculate totalCaloriesBurned and totalDuration
  let totalCaloriesBurned = 0;
  let totalDuration = 0;
  let planThumbnail = null;

  for (const section of filteredPlan.sections || []) {
    for (const ex of section.exercises || []) {
      totalCaloriesBurned += Number(ex.caloriesBurnt) || 0;
      totalDuration += Number(ex.duration) || 0;
      // Find the first non-null thumbnail
      if (!planThumbnail && ex.thumbnail) {
        planThumbnail = ex.thumbnail;
      }
    }
  }

  console.log(
    `[INFO] Workout plan for ${date} fetched successfully.`,
    `Calories: ${totalCaloriesBurned}, Duration: ${totalDuration}`,
    planThumbnail
  );

  return res.status(httpStatus.OK).send({
    message: `Workout plan for ${date} fetched successfully`,
    data: {
      ...filteredPlan,
      totalCaloriesBurned,
      totalDuration,
      planThumbnail,
    },
  });
});

// const startWorkout = catchAsync(async (req, res, next) => {
//   const userId = req.user._id;

//   const startTime = new Date();
//   const progressTracker = await progressTrackerService.createProgressTracker({
//     user: userId,
//     startTime,
//   });

//   return res.status(httpStatus.CREATED).send({ data: progressTracker });
// });

// const endWorkout = catchAsync(async (req, res, next) => {
//   const userId = req.user._id;
//   const now = new Date();

//   const startOfDay = moment(now)
//     .startOf("day")
//     .toDate();
//   const endOfDay = moment(now)
//     .endOf("day")
//     .toDate();

//   const progressTracker = await progressTrackerService.getTodayProgressTracker(
//     userId,
//     startOfDay,
//     endOfDay
//   );

//   if (!progressTracker || progressTracker.endTime) {
//     return res.status(httpStatus.BAD_REQUEST).send({
//       message: "No active workout session found or session already ended.",
//     });
//   }

//   // Calculate session duration in minutes
//   const sessionDuration = Math.round(
//     (now - progressTracker.startTime) / (1000 * 60)
//   );

//   progressTracker.endTime = now;
//   progressTracker.sessionDuration = sessionDuration; // Store session duration
//   await progressTracker.save();

//   return res.status(httpStatus.OK).send({ data: progressTracker });
// });

// const getProgressByDate = catchAsync(async (req, res, next) => {
//   const userId = req.user._id;
//   const { date } = req.query; // Expecting date in "YYYY-MM-DD" format

//   if (!date) {
//     return res
//       .status(httpStatus.BAD_REQUEST)
//       .send({ message: "Date is required in YYYY-MM-DD format." });
//   }

//   // Convert date to start & end of the day
//   const startOfDay = moment(date, "YYYY-MM-DD")
//     .startOf("day")
//     .toDate();
//   const endOfDay = moment(date, "YYYY-MM-DD")
//     .endOf("day")
//     .toDate();

//   const progress = await progressTrackerService.getProgressForDate(
//     userId,
//     startOfDay,
//     endOfDay
//   );

//   if (!progress) {
//     return res.status(httpStatus.NOT_FOUND).send({
//       message: "No progress found for this date.",
//     });
//   }

//   return res.status(httpStatus.OK).send({ data: progress });
// });

module.exports = {
  createWorkoutPlan,
  getCustomWorkoutPlan,
};
