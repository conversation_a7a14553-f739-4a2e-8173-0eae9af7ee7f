const catchAsync = require("../../utils/catchAsync");
const coinOfferService = require("../../services/coinOffer.service");
const ApiError = require("../../utils/ApiError");
const httpStatus = require("http-status");

const getCoinOffers = catchAsync(async (req, res, next) => {
    try {
        const coinOffers = await coinOfferService.getCoinOffers();
        res.status(200).send({ data: coinOffers, message: '' });
    } catch (error) {
        next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing Terms', error));
    }
});

const getCoinOffer = catchAsync(async (req, res, next) => {
    const coinOffer = await coinOfferService.getCoinOfferById(req.params.id);
    res.status(200).send({ data: coinOffer, message: '' });
});

const store = catchAsync(async (req, res, next) => {

    const coinOffer = await coinOfferService.createCoinOffer(req.body);
    res.status(201).send({ data: coinOffer, message: 'Coin Offer is created Successfully' });

});

const updateCoinOffer = catchAsync(async (req, res, next) => {

    const coinOffer = await coinOfferService.updateCoinOfferById(req.body._id, req.body);
    res.status(200).send({ data: coinOffer, message: 'Coin Offer is updated Successfully' });

});

const deleteCoinOffer = catchAsync(async (req, res, next) => {

    const coinOffer = await coinOfferService.deleteCoinOfferById(req.params.id);
    res.status(200).send({ data: coinOffer, message: 'Coin Offer is deleted Successfully' });

});

module.exports = {
    store,
    getCoinOffers,
    getCoinOffer,
    updateCoinOffer,
    deleteCoinOffer
}