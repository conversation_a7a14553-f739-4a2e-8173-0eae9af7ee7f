const openai = require("../config/openAi");

async function processCasualQuery(
  query,
  userData,
  chatHistory,
  userWorkoutPlan
) {
  try {
    if (!query || query.trim() === "") {
      throw new Error("Input query is empty. Please provide a valid query.");
    }

    // Define the system prompt for casual queries
    const prompt = `
      Here are the user Information use this to respond to the user:
      ${userData}

     ${chatHistory ? `Here is the chat history: ${chatHistory}` : ""} 
      
      ${
        userWorkoutPlan
          ? `Here is the existing workout plan for the user: ${userWorkoutPlan}`
          : ""
      } 

      Given the user information and chat history, respond to the user's query.
      Strict Response Guidelines:
      Acknowledge the user's query with chat history information make it as human like as possible.

      FOR SMALL CASUAL QUERIES:
      - If query is a short greeting or casual conversation starter
      - Respond briefly and warmly
    


       FOR QUESTIONS ABOUT YOU ARE YOU CHAT GPT OR OPEN AI QUERIES:
       - If user asks if you are a chatbot or AI
       - Respond with a you're TopFit's AI chatbot

      FOR FITNESS/HEALTH PERSONAL STATEMENTS:
      - If user mentions Health, meal, fitness, exercises, workout, diet, nutrients, food, food allergy without asking for help
      - Respond conversationally
      - Show empathy and understanding
      - DO NOT provide advice or suggestions
      - Reflect back their experience
      - Refer the existing workout plan for specific workout queries

      FOR OUT-OF-CONTEXT QUERIES:
      - If query is not related to casual conversation or fitness
      - Politely decline assistance
      - Explain you're only for casual fitness-related conversations
      - Acknowledge the user's query and but dont provoke to ask off topic questions politely decline assistance.

      ## IMPORTANT INFORMATION ##
      - Dont greet everytime , if the user is not using greetings , like hi,hello or something similar
      - if its related to the existing conversersation respond directly to the context , use no greetings

      Current Query: "${query.trim()}"
    `;

    // Call OpenAI API

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content:
            "You are a diet and fitness coach. Respond to queries related to diet and workouts.",
        },
        {
          role: "user",
          content: prompt,
        },
      ],
      temperature: 0.7,
    });

    // Extract and return the AI response
    const aiResponse = response.choices[0].message.content.trim();
    // console.log("AI Response:", aiResponse);
    return aiResponse;
  } catch (error) {
    throw new Error(`Failed to process casual query : ${error.message}`);
  }
}

module.exports = { processCasualQuery };
