const mongoose = require('mongoose');
const { paginate } = require('./plugins/paginate');

const likeSchema = new mongoose.Schema(
    {
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        postId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Post'
        }

    },
    { timestamps: true }
)

likeSchema.plugin(paginate);

const Like = mongoose.model('Like', likeSchema);
module.exports = {
    Like
};