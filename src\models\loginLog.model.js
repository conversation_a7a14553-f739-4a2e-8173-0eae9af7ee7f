const mongoose = require('mongoose');
const { paginate } = require('./plugins/paginate');

const loginLogSchema = new mongoose.Schema(
    {
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        }
    },
    { timestamps: true }
)

loginLogSchema.plugin(paginate);

loginLogSchema.statics.getGraphDataByWeekDays = async function(filters, week) {
    const today = new Date();
    let WeekStartDate;
    let WeekEndDate;
    const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    if(week == "last"){
      WeekStartDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay() - 6);
      WeekEndDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay());
    }else{
      WeekStartDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay());
      WeekEndDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() + (6 - today.getDay()));
    }
  
   
    const results = await this.aggregate([
      {
        $match: {
          ...filters,
          createdAt: {
            $gte: WeekStartDate,
            $lt: WeekEndDate,
          },
        },
      },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
          count: { $sum: 1 },
        },
      },
      {
        $sort: { _id: 1 },
      },
    ]);
    // Fill in missing months with zero completions
    const resultWithZeros = [];
    for (let i = 1; i <= 7; i++) {
      resultWithZeros.push({day: daysOfWeek[i-1], count: 0});
    }
    (results || []).forEach(({_id, count}) => {
      const date = new Date(_id);
      const day = date.getDay();
      resultWithZeros[day] = {day:daysOfWeek[day], count};
    });
    return resultWithZeros;
  };

const LoginLog = mongoose.model('LoginLog', loginLogSchema);
module.exports = {
    LoginLog
};