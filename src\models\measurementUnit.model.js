const mongoose = require('mongoose');
const { paginate } = require("./plugins/paginate");
const timestampPlugin = require("./plugins/timestampPlugin");

// Measurement Unit Schema
const measurementUnitSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  deletedAt: {
    type: Date,
    default: null
  },
  fullForm: {
    type: String,
    required: true
  }
});

measurementUnitSchema.plugin(paginate);
measurementUnitSchema.plugin(timestampPlugin);

module.exports.MeasurementUnit = mongoose.model("MeasurementUnit",measurementUnitSchema);
