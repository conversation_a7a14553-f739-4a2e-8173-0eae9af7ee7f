const mongoose = require('mongoose');
const { paginate } = require('./plugins/paginate');

const bmiTimelineSchema = new mongoose.Schema(
    {
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        image: {
            type: {
                key: String,
                url: String,
            },
            default: null,
        },
        weight: {
            type: Number,
            default: null,
        },
        height: {
            type: Number,
            default: null,
        },
        bmi: {
            type: Number,
            default: null,
        },
        month:{
            type: String,
            default: null,
        },
        year:{
            type: String,
            default: null,
        }
    },
    { timestamps: true }
)

bmiTimelineSchema.plugin(paginate);

const BmiTimeline = mongoose.model('BmiTimeline', bmiTimelineSchema);
module.exports = {
    BmiTimeline
};