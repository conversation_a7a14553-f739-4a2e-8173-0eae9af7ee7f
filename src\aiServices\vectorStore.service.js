const e = require("cors");
const openai = require("../config/openAi");
const { CustomDietPlan } = require("../models/customDietPlanModel");
const { VectorStore } = require("../models/openAiVectorStore");
const { User } = require("../models/user.model");
const fs = require("fs");
const path = require("path");
const util = require("util");
const { calculateAge } = require("../utils/calculateAge");
const writeFile = util.promisify(fs.writeFile);
const unlink = util.promisify(fs.unlink);

// Upload files to the vector store

async function uploadFileToVectorStore(
  jsonlData,
  userId,
  userName,
  fileType = "diet",
  existingPlan
) {
  try {
    if (existingPlan?.vectorFileId) {
      console.log(`🗑️ Deleting existing file: ${existingPlan.vectorFileId}`);
      await openai.files.del(existingPlan.vectorFileId);
    }

    const filePath = path.join(__dirname, `${fileType}_plan_${userId}.json`);

    await writeFile(filePath, jsonlData, "utf-8");

    // Open file as stream
    const fileStream = fs.createReadStream(filePath);

    // Upload file to OpenAI
    const fileResponse = await openai.files.create({
      file: fileStream,
      purpose: "assistants",
    });

    // Attach file to vector store
    const vectorStore = await getOrCreateVectorStore();
    await openai.beta.vectorStores.files.create(vectorStore.vectorStoreId, {
      file_id: fileResponse.id,
    });

    console.log("✅ File uploaded successfully:", {
      fileId: fileResponse.id,
      userId,
      userName,
      fileType,
    });

    console.log(`🧹 Cleaning up temporary file: ${filePath}`);
    await unlink(filePath);

    return { fileId: fileResponse.id, userId, userName, fileType };
  } catch (error) {
    console.error("❌ Error uploading file to vector store:", error.message);
    throw error;
  }
}

async function getOrCreateVectorStore() {
  let vectorStore = await VectorStore.findOne();
  if (!vectorStore) {
    console.log("creating vector store as not found");

    const response = await openai.beta.vectorStores.create({
      name: "Fitness and Diet Plans",
    });
    vectorStore = new VectorStore({
      vectorStoreId: response.id,
      name: "Fitness and Diet Plans",
    });
    await vectorStore.save();
  }
  return vectorStore;
}

async function getOrCreateAssistant(
  user,
  vectorStore,
  workoutFileID,
  dietPlanFileId
) {
  console.log(workoutFileID, dietPlanFileId);
  if (!user.assistantId) {
    const age = calculateAge(user.dob);

    const response = await openai.beta.assistants.create({
      name: "Fitness and Diet Assistant",
      instructions: `## Fitness and Diet Assistant ##
      
### General Behavior
1. **Use stored files only when checking for existing diet and workout plans.**
2. **Search ONLY files matching:**  
   - **User ID:** ${user._id.toString()}  
   - **User Name:** ${user.name}  
   - **Workout File ID:** ${workoutFileID}  
   - **Diet File ID:** ${dietPlanFileId}

3. **STRICT MATCHING:**  
   - **No partial matches** allowed.  
   - **If a file does not match EXACTLY, ignore it.**     
4. **Keep responses concise and to the point.**
5. **DO NOT create new plans, include file references, or provide external links.**
6. **Ensure responses do not contain file references like filenames, IDs, or metadata. or any sort of reference to vector file name or ID**
7. **If the files are marked "not-avaliable" dont look for those files. - reply for file related queries deny politely as no plan exists**
8. **Strictly match name and the file ids , no partial matching is allowed**
    
### User Details:
- Name: ${user.name}
- Age: ${age}
- Height: ${user.height} cm
- Gender: ${user.gender}
- Fitness Goals: ${user.fitnessGoals.join(", ")}
- Weight: ${user.weight} ${user.weightUnit}
- Training Environment: ${user.trainingEnvironment.join(", ")}
- Exercise Session Duration: ${user.exerciseSessionDuration} minutes
- Medical Conditions: ${
        user.medicalConditions.length > 0
          ? user.medicalConditions.join(", ")
          : "None"
      }
- Meal Type: ${
        user.mealType.length > 0 ? user.mealType.join(", ") : "Not specified"
      }
- Diet Preference: ${
        user.dietPreference ? user.dietPreference : "Not specified"
      }
- Specific Fitness Targets: ${
        user.specificFitnesstargets.length > 0
          ? user.specificFitnesstargets.join(", ")
          : "Not specified"
      }
- Allergies: ${user.allergies.length > 0 ? user.allergies.join(", ") : "None"}
- Dietary Restrictions: ${
        user.dietaryRestrictions.length > 0
          ? user.dietaryRestrictions.join(", ")
          : "None"
      }
  
    
### Response Rules:
  - **Casual Greetings:** Respond warmly but briefly.
  - **AI Identity Queries:** "I'm TopFit's AI assistant, here to help with fitness and diet and create diet and workout plans. "
  - **Fitness/Health Statements:** Show empathy but do not give unsolicited advice.
  - **Direct Fitness/Diet Questions:** Check stored files **only if needed**, otherwise respond with general user data.
  - **Out-of-Context Queries:** Politely decline and redirect to fitness topics.
  - **If files are marked as "not-available", do NOT search for them and reply: "deny politely as no plan exists."**
  - **No partial matches or alternative files should be used.**

### STRICT FILE MATCHING RULES :
    **NEVER reference non-matching files.**  
    **NEVER include file IDs, filenames, or metadata in responses.**  
    **If a requested file is missing, reply: "deny politely as no plan exists."**  
    **STRICTLY match name and file IDs. No partial matches allowed.**  


### Important Guidelines:
  **Strictly match name and the file ids , no partial matching is allowed**
  **No greetings unless user greets first.**
  **Keep answers short and relevant.**
  **Avoid repetitive introductions.**
  **Do not answer topics outside of fitness and diet. Decline politely**
  **Do not provide medical advice.**
  **Do not provide unsolicited advice.**
  **Do not provide external links.**
  **Do not include any additional fields in the JSON other than the ones mentioned above.**
  **Do not include source of information in the response**
  **If the file are marked as "not-avaliable" dont look for those files. - reply for file related queries as deny politely as no plan exists**


#####  Invalid Responses  ##### 
  EXAMPLE 1. response - "Yes, your diet plan includes a Paneer Sandwich for breakfast with a serving size of 200g【12:0†diet_plan_655c5e3f067eeb924e47194f.json】"
  reason - contains the direct reference to the reffered vector file
  EXAMPLE 2. response -  "Yes, your workout plan includes the Bench Press. It is scheduled on January 16th【66:0†workout_plan_655c5e3f067eeb924e47194f.json】".
  reason - contains the direct reference to the reffered vector file


#####  Your Job  ##### 
  Only answer using information found in the user’s diet and workout plan files, and only if the file name and file ID exactly match those provided above.
  If the files are not available, or do not match exactly, respond: "I'm sorry, but no [diet/workout] plan exists for you currently."
  Never mention, reference, or hint at file names, file IDs, file metadata, or the existence of other files, even if found.
  Never generate new plans, suggestions, or sample workouts/diets. If the user asks, reply politely that no plan exists.
  Never show, include, or hint at file citations, filenames, IDs, or any technical detail about the storage or retrieval process.
  Never attempt partial matches or fallback to alternative files — exact match only, as described above.
  Never mention or leak the file search process in any way.
  Never add source information, vector file info, or anything similar in the output.
  Keep responses clear, relevant, and brief. No extra information, commentary, or external links

      `,
      model: "gpt-4.1",
      tools: [{ type: "file_search" }],
      tool_resources: {
        file_search: { vector_store_ids: [vectorStore.vectorStoreId] },
      },
    });

    user.assistantId = response.id;
    await user.save();
  }
  return user.assistantId;
}

// Function to get or create a thread for a user
async function getOrCreateThread(user) {
  if (!user.threadId) {
    const response = await openai.beta.threads.create();
    user.threadId = response.id;
    console.log("its here 1");
    await user.save();
    console.log("its here 2");
  }
  return user.threadId;
}

// Process user query
async function processQuery(userData, query, workoutFileId, dietPlanFileId) {
  const vectorStore = await getOrCreateVectorStore();
  console.log(vectorStore.vectorStoreId, "<<<<<<<<<<<<<<<");
  const assistantId = await getOrCreateAssistant(
    userData,
    vectorStore,
    workoutFileId,
    dietPlanFileId
  );
  const threadId = await getOrCreateThread(userData);

  console.time("Message Creation Time");

  await openai.beta.threads.messages.create(threadId, {
    role: "user",
    content: query,
  });

  console.timeEnd("Message Creation Time");
  console.time("Run Creation and Polling Time");

  const run = await openai.beta.threads.runs.createAndPoll(threadId, {
    assistant_id: assistantId,
  });

  console.timeEnd("Run Creation and Polling Time");

  console.log(run);

  if (run.status === "completed") {
    const messages = await openai.beta.threads.messages.list(threadId);

    // Check if messages exist before accessing properties
    if (!messages?.data?.length || !messages.data[0]?.content?.length) {
      console.error("No valid messages received from assistant.");
      return { error: "No valid response from assistant" };
    }

    // Extract AI response text
    let responseText = messages.data[0].content[0].text.value;

    console.log(responseText, "<<<<<<<<<<<<<");

    responseText = responseText.replace(/【\d+:\d+†[^】]+】/g, "");

    console.log(responseText, "<<<<<<<<<<<<<");
    return responseText;
  } else {
    throw new Error("Failed to process query");
  }
}

async function getChatHistoryFromThread(threadId) {
  try {
    console.log(`📜 Retrieving chat history for thread: ${threadId}`);
    const messages = await openai.beta.threads.messages.list(threadId);

    messages.data.forEach((message, index) => {
      console.log(`💬 Message ${index + 1} - Role: ${message.role}`);
      console.log(`📝 Content: ${message.content}`);
    });

    return messages.data;
  } catch (error) {
    console.error("❌ Error retrieving chat history:", error.message);
    throw error;
  }
}

async function updateAssistant(
  assistantId,
  vectorStoreId,
  instructions,
  userId
) {
  try {
    let assistant;

    if (!assistantId) {
      console.warn("ℹ️ No assistant ID found. Creating a new assistant...");

      assistant = await openai.beta.assistants.create({
        name: "Fitness and Diet Assistant",
        instructions: instructions,
        model: "gpt-4o",
        tools: [{ type: "file_search" }],
        tool_resources: {
          file_search: {
            vector_store_ids: [vectorStoreId],
          },
        },
      });

      console.log("✅ New assistant created:", assistant.id);

      // Save assistant ID to user
      await User.findByIdAndUpdate(userId, { assistantId: assistant.id });

      return assistant;
    }

    // Assistant ID exists — update the assistant
    const response = await openai.beta.assistants.update(assistantId, {
      name: "Fitness and Diet Assistant",
      instructions: instructions,
      tool_resources: {
        file_search: {
          vector_store_ids: [vectorStoreId],
        },
      },
    });

    console.log("✅ Assistant updated successfully:", response);
    return response;
  } catch (error) {
    console.error("❌ Error in updateAssistant:", error.message);
    throw error;
  }
}

const createInstructions = (
  user,
  workoutFileID = "not-avaliable",
  dietPlanFileID = "not-avaliable"
) => {
  const age = calculateAge(user.dob);

  console.log(workoutFileID, dietPlanFileID, "look here");

  return `## Fitness and Diet Assistant ##

### General Behavior
1. **Use stored files only when checking for existing diet and workout plans.**
2. **Search ONLY files matching:**  
   - **User ID:** ${user._id.toString()}  
   - **User Name:** ${user.name}  
   - **Workout File ID:** ${workoutFileID}  
   - **Diet File ID:** ${dietPlanFileID}

3. **STRICT MATCHING:**  
   - **No partial matches** allowed.  
   - **If a file does not match EXACTLY, ignore it.** 

4. **Keep responses concise and to the point.**
5. **DO NOT create new plans, include file references, or provide external links.**
6. **Ensure responses do not contain file references like filenames, IDs, or metadata. or any sort of reference to vector file name or ID**
7. **If the files are marked "not-avaliable" dont look for those files. - reply for file related queries deny politely as no plan exists**
8. **Strictly match name and the file ids , no partial matching is allowed**
    
### User Details:
- Name: ${user.name}
- Age: ${age}
- Height: ${user.height} cm
- Gender: ${user.gender}
- Fitness Goals: ${user.fitnessGoals.join(", ")}
- Weight: ${user.weight} ${user.weightUnit}
- Training Environment: ${user.trainingEnvironment.join(", ")}
- Exercise Session Duration: ${user.exerciseSessionDuration} minutes
- Medical Conditions: ${
    user.medicalConditions.length > 0
      ? user.medicalConditions.join(", ")
      : "None"
  }
- Meal Type: ${
    user.mealType.length > 0 ? user.mealType.join(", ") : "Not specified"
  }
- Diet Preference: ${
    user.dietPreference ? user.dietPreference : "Not specified"
  }
- Specific Fitness Targets: ${
    user.specificFitnesstargets.length > 0
      ? user.specificFitnesstargets.join(", ")
      : "Not specified"
  }
- Allergies: ${user.allergies.length > 0 ? user.allergies.join(", ") : "None"}
- Dietary Restrictions: ${
    user.dietaryRestrictions.length > 0
      ? user.dietaryRestrictions.join(", ")
      : "None"
  }
  
    
### Response Rules:
  - **Casual Greetings:** Respond warmly but briefly.
  - **AI Identity Queries:** "I'm TopFit's AI assistant, here to help with fitness and diet and create diet and workout plans. "
  - **Fitness/Health Statements:** Show empathy but do not give unsolicited advice.
  - **Direct Fitness/Diet Questions:** Check stored files **only if needed**, otherwise respond with general user data.
  - **Out-of-Context Queries:** Politely decline and redirect to fitness topics.
  - **If files are marked as "not-available", do NOT search for them and reply: "deny politely as no plan exists."**
  - **No partial matches or alternative files should be used.**

  ### STRICT FILE MATCHING RULES :
    **NEVER reference non-matching files.** 
    **NEVER include file IDs, filenames, or metadata in responses.**  
    **If a requested file is missing, reply: "deny politely as no plan exists."**  
    **STRICTLY match name and file IDs. No partial matches allowed.**  


### Important Guidelines:
  **Strictly match name and the file ids , no partial matching is allowed**
  **No greetings unless user greets first.**
  **Keep answers short and relevant.**
  **Avoid repetitive introductions.**
  **Do not answer topics outside of fitness and diet. Decline politely**
  **Do not provide medical advice.**
  **Do not provide unsolicited advice.**
  **Do not provide external links.**
  **If the file are marked as "not-avaliable" dont look for those files. - reply for file related queries as deny politely as no plan exists**


#####  Invalid Responses  ##### 
  EXAMPLE 1. response - "Yes, your diet plan includes a Paneer Sandwich for breakfast with a serving size of 200g【12:0†diet_plan_655c5e3f067eeb924e47194f.json】"
  reason - contains the direct reference to the reffered vector file
  EXAMPLE 2. response -  "Yes, your workout plan includes the Bench Press. It is scheduled on January 16th【66:0†workout_plan_655c5e3f067eeb924e47194f.json】".
  reason - contains the direct reference to the reffered vector file

#####  Your Job  ##### 
  Only answer using information found in the user’s diet and workout plan files, and only if the file name and file ID exactly match those provided above.
  If the files are not available, or do not match exactly, respond: "I'm sorry, but no diet/workout plan exists for you currently."
  Never mention, reference, or hint at file names, file IDs, file metadata, or the existence of other files, even if found.
  Never generate new plans, suggestions, or sample workouts/diets. If the user asks, reply politely that no plan exists.
  Never show, include, or hint at file citations, filenames, IDs, or any technical detail about the storage or retrieval process.
  Never attempt partial matches or fallback to alternative files — exact match only, as described above.
  Never mention or leak the file search process in any way.
  Never add source information, vector file info, or anything similar in the output.
  Keep responses clear, relevant, and brief. No extra information, commentary, or external links
`;
};

module.exports = {
  processQuery,
  getOrCreateThread,
  getOrCreateAssistant,
  uploadFileToVectorStore,
  getChatHistoryFromThread,
  getOrCreateVectorStore,
  getOrCreateAssistant,
  updateAssistant,
  createInstructions,
};
