const catchAsync = require("../../utils/catchAsync");
const equipmentService = require("../../services/equipment.service");

const getEquipments = catchAsync(async (req, res, next) => {
    try {
        const { sortOrder, keyword, timeFrame, limit, page } = req.query;
        const equipments = await equipmentService.getEquipments(sortOrder, keyword, timeFrame, { limit, page, sortOrder, sortBy: "createdAt" });
        res.status(200).send({ data: equipments, message: '' });
    } catch (error) {
        next(new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error listing equipments', error));
    }
});

const getEquipment = catchAsync(async (req, res, next) => {

    const equipment = await equipmentService.getEquipmentById(req.params.id);
    res.status(200).send({ data: equipment, message: '' });

});

const store = catchAsync(async (req, res, next) => {

    const equipment = await equipmentService.createEquipment(req.body, req.file);
    res.status(201).send({ data: equipment, message: 'Equipment is created Successfully' });

});

const updateEquipment = catchAsync(async (req, res, next) => {

    const equipment = await equipmentService.updateEquipmentById(req.body._id, req.body, req.file);
    res.status(200).send({ data: equipment, message: 'Equipment is updated Successfully' });

});

const deleteEquipment = catchAsync(async (req, res, next) => {

    const equipment = await equipmentService.deleteEquipmentById(req.params.id);
    res.status(200).send({ data: equipment, message: 'Equipment is deleted Successfully' });

});

module.exports = {
    store,
    getEquipment,
    getEquipments,
    updateEquipment,
    deleteEquipment
}