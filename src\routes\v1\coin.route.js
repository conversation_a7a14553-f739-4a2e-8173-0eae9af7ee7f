const express = require('express');
const router = express.Router();

const coinController = require('../../controllers/coin.controller');
const { firebaseAuth } = require('../../middlewares/firebaseAuth');

router.post(
    '/buy',
    firebaseAuth,
    coinController.buyCoins
);

router.get(
    '/walletBalance',
    firebaseAuth,
    coinController.walletBalance
);

router.get(
    '/transactions',
    firebaseAuth,
    coinController.walletTransactions
);

module.exports = router;