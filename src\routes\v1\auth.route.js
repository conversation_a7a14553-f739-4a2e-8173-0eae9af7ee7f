const express = require("express");
const router = express.Router();

const validate = require("../../middlewares/validate");
const {
  firebaseAuth,
  generateToken,
} = require("../../middlewares/firebaseAuth");
const authValidation = require("../../validations/auth.validation");

const { authController, loginlogController } = require("../../controllers");

router.post("/login", firebaseAuth, authController.loginUser);

router.post(
  "/onBoarding",
  firebaseAuth,
  // validate(authValidation.register),
  authController.onBoarding
);

router.get("/generateToken/:uid", generateToken);

router.get("/log", firebaseAuth, loginlogController.createLog);
module.exports = router;
