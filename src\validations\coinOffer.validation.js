const Joi = require('joi');
const { objectId } = require('./custom.validation');

const coinOfferSchema = {
    title: Joi.string(),
    coinNumber: Joi.number(),
    price: Joi.number(),
    totalSold: Joi.number(),
    Status: Joi.string()
};


const AddCoinOffer = {
    body: Joi.object().keys({
        ...coinOfferSchema,
    }),
};

const EditCoinOffer = {
    body: Joi.object().keys({
        _id: Joi.string()
            .custom(objectId),
        ...coinOfferSchema,
    }),
};

module.exports = {
    AddCoinOffer,
    EditCoinOffer
};
