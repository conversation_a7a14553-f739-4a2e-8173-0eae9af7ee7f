const { LoginLog } = require('../models/loginLog.model');

async function createLog(userId) {

    // Calculate the start of today
    const startOfToday = new Date();
    startOfToday.setHours(0, 0, 0, 0);

    // Calculate the end of today
    const endOfToday = new Date();
    endOfToday.setHours(23, 59, 59, 999);
    let log = await LoginLog.findOne({userId,createdAt: { $gte: startOfToday, $lte: endOfToday }});
    if(!log){
        log = await LoginLog.create({userId});
    }
    return log;
}

module.exports = {
    createLog
}