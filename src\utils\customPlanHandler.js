const { s3Upload } = require("../microservices/fileUpload.service");
const { CustomDietPlan } = require("../models/customDietPlanModel");

// const addWorkoutPlanToS3 = async (workoutPlan, userId) => {
//   try {
//     console.log("🔧 Adding workout plan to S3...");
//     // Process the workout plan content (if needed)
//     const processedContent = await processWorkoutPlan(workoutPlan);
//     if (!processedContent) {
//       throw new Error(
//         `Content processing failed for Workout Plan ID: ${workoutPlan._id}`
//       );
//     }

//     // Convert workout plan to JSONL format
//     const jsonlData = processedContent
//       .map((entry) => JSON.stringify(entry))
//       .join("\n");

//     // Upload updated workout plan to S3 using s3Upload with UUID-generated keys
//     const files = [
//       {
//         originalname: `workoutplan-${userId}.json`,
//         buffer: Buffer.from(jsonlData, "utf-8"),
//       },
//     ];

//     const uploadResults = await s3Upload(files, "workoutPlans");
//     console.log(uploadResults);
//     // Update the DB with the S3 URL

//     console.log("🔧 Added workout plan to S3...", uploadResults);

//     return { uploadResults };
//   } catch (error) {
//     console.error("❌ Error adding workout plan to S3:", error.message);
//     throw error;
//   }
// };
async function processWorkoutPlan(workoutPlan, userId, name) {
  try {
    const processedContent = workoutPlan.map((day) => {
      return {
        userId,
        name,
        date: day.date,
        description: day.description,
        isLeave: day.isLeave || false,
        workout_plan: day.sections.map((section) => ({
          section: section.section,
          exercises: section.exercises.map((exercise) => ({
            name: exercise.name,
            equipment: exercise.equipment || [],
            weight: exercise.weight || null,
            sets: exercise.sets || null,
            reps: exercise.reps || null,
            caloriesBurnt: exercise.caloriesBurnt || null,
            duration: exercise.duration || null,
            media: {
              thumbnail: exercise.thumbnail || null,
              video: exercise.video || null,
            },
            extraDetails: {
              // Fallbacks from populated _id (optional)
              description: exercise._id?.description || null,
              calories: exercise._id?.calories || null,
            },
          })),
        })),
      };
    });

    const jsonlData = processedContent
      .map((entry) => JSON.stringify(entry))
      .join("\n");

    return jsonlData;
  } catch (error) {
    console.error("⚠️ Error processing workout plan:", error.message);
    return null;
  }
}

// const addDietPlanToS3 = async (userId) => {
//   try {
//     console.log("🔧 Adding diet plan to S3...");

//     const dietPlan = await CustomDietPlan.findOne({ user: userId })
//       .populate({
//         path: "dietPlan.breakfast._id", // Populate breakfast meal's recipe
//       })
//       .populate({
//         path: "dietPlan.lunch._id", // Populate lunch meal's recipe
//         // Adjust fields based on the Recipe model
//       })
//       .populate({
//         path: "dietPlan.dinner._id", // Populate dinner meal's recipe
//       })
//       .lean();
//     console.log(dietPlan);
//     // Process the diet plan content
//     const processedContent = await processDietPlan(dietPlan.dietPlan);
//     if (!processedContent) {
//       throw new Error(
//         `Content processing failed for Diet Plan ID: ${dietPlan._id}`
//       );
//     }

//     // Convert diet plan to JSONL format
//     const jsonlData = processedContent
//       .map((entry) => JSON.stringify(entry))
//       .join("\n");

//     // Upload the diet plan to S3
//     const files = [
//       {
//         originalname: `dietplan-${userId}.json`,
//         buffer: Buffer.from(jsonlData, "utf-8"),
//         contentType: "application/json", // <-- Add this line
//       },
//     ];

//     const uploadResults = await s3Upload(files, "dietPlans");
//     console.log("✅ Diet plan uploaded to S3:", uploadResults);

//     return uploadResults[0];
//   } catch (error) {
//     console.error("❌ Error adding diet plan to S3:", error.message);
//     throw error;
//   }
// };

// Helper function to process the diet plan
async function processDietPlan(dietPlan, userId, name) {
  try {
    const processedContent = dietPlan.map((day) => ({
      userId,
      name,
      date: day.date,
      isLeave: day.isLeave,
      breakfast: {
        meal: day.breakfast?.meal,
        servingSize: day.breakfast?.servingSize,
        details: day.breakfast._id
          ? {
              description: day.breakfast.description,
              calories: day.breakfast.calories,
              ingredients: day.breakfast.ingredients?.map((ingredient) => ({
                name: ingredient.name,
                measurement: ingredient.measurement,
                calories: ingredient.calories,
              })),
              nutritionalValue: day.breakfast.nutritionalValue,
            }
          : null,
      },
      lunch: {
        meal: day.lunch?.meal,
        servingSize: day.lunch?.servingSize,
        details: day.lunch._id
          ? {
              description: day.lunch.description,
              calories: day.lunch.calories,
              ingredients: day.lunch.ingredients?.map((ingredient) => ({
                name: ingredient.name,
                measurement: ingredient.measurement,
                calories: ingredient.calories,
              })),
              nutritionalValue: day.lunch.nutritionalValue,
            }
          : null,
      },
      dinner: {
        meal: day.dinner?.meal,
        servingSize: day.dinner?.servingSize,
        details: day.dinner._id
          ? {
              description: day.dinner.description,
              calories: day.dinner.calories,
              ingredients: day.dinner.ingredients?.map((ingredient) => ({
                name: ingredient.name,
                measurement: ingredient.measurement,
                calories: ingredient.calories,
              })),
              nutritionalValue: day.dinner.nutritionalValue,
            }
          : null,
      },
    }));

    const jsonlData = processedContent
      .map((entry) => JSON.stringify(entry))
      .join("\n");
    return jsonlData;
  } catch (error) {
    console.error("⚠️ Error processing diet plan:", error.message);
    return null;
  }
}

module.exports = {
  // addWorkoutPlanToS3,
  // addDietPlanToS3,
  processDietPlan,
  processWorkoutPlan,
};
