const { User, Client } = require("../models");
const httpStatus = require("http-status");
const ApiError = require("../utils/ApiError");
const { fileUploadService } = require("../microservices");
const { WorkoutPlan } = require("../models/workoutPlan.model");
const { getAllData } = require("../utils/getAllData");
const { UserWorkout } = require("../models/userWorkout.model");
const { filter } = require("compression");

async function getWorkoutPlanById(id) {
  const workoutPlan = await WorkoutPlan.findById(id)
    .populate("section.exercises.exercise")
    .populate("equipment");
  return workoutPlan;
}

async function getWorkoutPlans(
  sortOrder = "desc",
  keyword = "",
  timeFrame = "all",
  options
) {
  try {
    let filter = {};

    // Filtering based on keyword (if provided)
    if (keyword) {
      filter.$or = [
        { name: { $regex: keyword, $options: "i" } }, // Case-insensitive match for 'name'
        { description: { $regex: keyword, $options: "i" } }, // Case-insensitive match for 'description'
        { type: { $regex: keyword, $options: "i" } }, // Case-insensitive match for 'type'

        // Add more fields as needed for the keyword search
      ];
    }

    // Apply filters to the aggregation pipeline
    let pipeline = [];

    // Filtering based on timeFrame
    if (timeFrame !== "all") {
      const now = new Date();
      let startDate;
      switch (timeFrame) {
        case "today":
          startDate = new Date(
            now.getFullYear(),
            now.getMonth(),
            now.getDate()
          );
          break;
        case "thisWeek":
          startDate = new Date(
            now.getFullYear(),
            now.getMonth(),
            now.getDate() - now.getDay()
          );
          break;
        case "thisMonth":
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case "thisYear":
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
        default:
          startDate = new Date(0); // To include all dates (past and present)
      }
      filter.createdAt = { $gte: startDate, $lte: now };
    }

    // Aggregate query
    const plans = await WorkoutPlan.paginate(filter, options);

    return plans;
  } catch (error) {
    // console.log(error)
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      "Error listing workoutPlans"
    );
  }
}

async function createWorkoutPlan(details, image) {
  let data = { ...details };

  if (image) {
    const [thumbnail] = await fileUploadService
      .s3Upload([image], "thumbnail")
      .catch((err) => {
        throw new ApiError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Failed to upload thumbnail"
        );
      });
    data = { ...data, thumbnail };
  }

  return await WorkoutPlan.create(data);
}

async function updateWorkoutPlanById(id, body, image) {
  const workoutPlan = await WorkoutPlan.findById(id);
  let updates = { ...body };

  if (image) {
    const [thumbnail] = await fileUploadService
      .s3Upload([image], "thumbnail")
      .catch((err) => {
        throw new ApiError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Failed to upload thumbnail"
        );
      });
    if (workoutPlan.thumbnail) {
      const oldPicKey = workoutPlan.thumbnail.key;
      await fileUploadService
        .s3Delete(oldPicKey)
        .catch((err) => console.log("Failed to delete thumbnail", oldPicKey));
    }
    updates = { ...updates, thumbnail };
  }

  return await WorkoutPlan.findByIdAndUpdate(id, updates, { new: true });
}

async function deleteWorkoutPlanById(id) {
  try {
    await WorkoutPlan.findByIdAndDelete(id);
    return true;
  } catch (err) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      "Failed to delete the WorkoutPlan"
    );
  }
}

// get all workout plans
async function getAllWorkoutPlanWithCustomFilters(filters, options) {
  const data = WorkoutPlan.paginate(filters, options); //await getAllData(WorkoutPlan, query, populateConfig)
  return data;
}

// Save User Workout Plan
async function createUserWorkoutPlan(body) {
  const data = await UserWorkout.create(body);
  return data;
}

async function getAllPlanExcercies(id) {
  const data = await WorkoutPlan.findById(id).populate(
    "section.exercises.exercise"
  );

  if (!data) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      "Invalid WorkoutPlan Id"
    );
  }

  let exercises = [];

  data.section.forEach((sec) => {
    exercises = [...exercises, ...sec.exercises];
  });

  return exercises;
}

async function updateUserWorkoutById(id, body) {
  return await UserWorkout.findByIdAndUpdate(id, body, { new: true });
}

// Get User WorkoutPlan by Id
async function getUserWorkoutPlanById(id) {
  const workoutPlan = await UserWorkout.findById(id).populate("workoutPlanId");
  return workoutPlan;
}

async function getUserWorkouts(userId) {
  const workoutPlan = await UserWorkout.find({ userId })
    .populate("workoutPlanId")
    .sort({ createdAt: -1 });
  return workoutPlan;
}

function generateDateRange(year, month) {
  const date = new Date(year, month - 1, 1);
  const dates = [];

  while (date.getMonth() === month - 1) {
    dates.push(new Date(date));
    date.setDate(date.getDate() + 1);
  }

  return dates;
}

const getUserWorkoutsForMonth = async (userId, month, year) => {
  const startDate = new Date(year, month - 1, 1);
  const endDate = new Date(year, month, 0);
  const dateRange = generateDateRange(year, month);

  // Fetch the workout data from MongoDB
  const workouts = await UserWorkout.aggregate([
    {
      $match: {
        userId: userId,
        startTime: {
          $gte: startDate,
          $lte: endDate,
        },
      },
    },
    {
      $lookup: {
        from: "workoutplans", // Name of the WorkoutPlan collection
        localField: "workoutPlanId",
        foreignField: "_id",
        as: "workoutPlanDetails",
      },
    },
    {
      $unwind: {
        path: "$workoutPlanDetails",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        date: {
          $dateToString: { format: "%Y-%m-%d", date: "$startTime" },
        }, // Format date as "YYYY-MM-DD"
        duration: 1,
        calories: 1,
        workoutPlan: "$workoutPlanDetails",
        //  {
        //     _id: "$workoutPlanDetails._id",
        //     name: "$workoutPlanDetails.name",
        //     description: "$workoutPlanDetails.description",

        // },
      },
    },
    {
      $group: {
        _id: "$date", // Group by date string
        totalDuration: { $sum: "$duration" },
        totalCalories: { $sum: "$calories" },
        count: { $sum: 1 },
        workouts: {
          $push: {
            workoutPlan: "$workoutPlan",
            duration: "$duration",
            calories: "$calories",
          },
        },
      },
    },
    {
      $sort: { _id: 1 }, // Sort by date
    },
  ]);

  // Create a map of workout data keyed by date
  const workoutMap = workouts.reduce((map, workout) => {
    map[workout._id] = workout;
    return map;
  }, {});

  // Merge the generated dates with the workout data
  const result = dateRange.map((date) => {
    const formattedDate = date.toISOString().split("T")[0]; // Format as "YYYY-MM-DD"
    return (
      workoutMap[formattedDate] || {
        _id: formattedDate,
        totalDuration: 0,
        totalCalories: 0,
        count: 0,
        workouts: [],
      }
    );
  });

  return result;
};

const getUserWorkoutsForDay = async (userId, day, month, year) => {
  const startDate = new Date(Date.UTC(year, month - 1, day));
  const endDate = new Date(Date.UTC(year, month - 1, day, 23, 59, 59)); // End of day in UTC

  console.log(
    `📅 Fetching workouts for: ${startDate.toISOString()} - ${endDate.toISOString()}`
  );

  // Fetch the workout data from MongoDB for the specific day
  const workouts = await UserWorkout.aggregate([
    {
      $match: {
        userId: userId,
        startTime: {
          $gte: startDate,
          $lte: endDate,
        },
      },
    },
    {
      $lookup: {
        from: "workoutplans", // Lookup for default plans
        localField: "workoutPlanId",
        foreignField: "_id",
        as: "workoutPlanDetails",
      },
    },
    {
      $lookup: {
        from: "customworkoutplans", // Lookup for custom plans
        localField: "customPlanId",
        foreignField: "_id",
        as: "customWorkoutPlanDetails",
      },
    },
    {
      $addFields: {
        workoutPlan: {
          $cond: {
            if: { $gt: [{ $size: "$workoutPlanDetails" }, 0] },
            then: { $arrayElemAt: ["$workoutPlanDetails", 0] },
            else: { $arrayElemAt: ["$customWorkoutPlanDetails", 0] },
          },
        },
      },
    },
    {
      $lookup: {
        from: "exercises", // Lookup for performed exercises
        localField: "exercisesPerformed.exerciseId",
        foreignField: "_id",
        as: "exerciseDetails",
      },
    },
    {
      $addFields: {
        exercisesPerformed: {
          $map: {
            input: "$exercisesPerformed",
            as: "exercise",
            in: {
              _id: "$$exercise._id",
              sets: "$$exercise.sets",
              reps: "$$exercise.reps",
              weight: "$$exercise.weight",
              caloriesBurnt: { $ifNull: ["$$exercise.caloriesBurnt", 0] },
              details: {
                $arrayElemAt: [
                  "$exerciseDetails",
                  {
                    $indexOfArray: [
                      "$exerciseDetails._id",
                      "$$exercise.exerciseId",
                    ],
                  },
                ],
              },
            },
          },
        },
      },
    },
    {
      $project: {
        date: {
          $dateToString: { format: "%Y-%m-%d", date: "$startTime" },
        },
        duration: { $ifNull: ["$duration", 0] },
        calories: { $ifNull: ["$calories", 0] },
        exercisesPerformed: 1,
      },
    },
    {
      $group: {
        _id: "$date",
        totalDuration: { $sum: "$duration" },
        totalCalories: { $sum: "$calories" },
        count: { $sum: 1 },
        workouts: {
          $push: {
            duration: "$duration",
            calories: "$calories",
            exercisesPerformed: "$exercisesPerformed",
          },
        },
      },
    },
  ]);

  // If there are no workouts for the day, return a default response
  if (workouts.length === 0) {
    const formattedDate = startDate.toISOString().split("T")[0]; // Format as "YYYY-MM-DD"
    return [
      {
        _id: formattedDate,
        totalDuration: 0,
        totalCalories: 0,
        count: 0,
        workouts: [],
      },
    ];
  }

  return workouts;
};

module.exports = {
  getWorkoutPlanById,
  getWorkoutPlans,
  createWorkoutPlan,
  updateWorkoutPlanById,
  deleteWorkoutPlanById,
  getAllWorkoutPlanWithCustomFilters,
  createUserWorkoutPlan,
  getUserWorkoutPlanById,
  updateUserWorkoutById,
  getAllPlanExcercies,
  getUserWorkouts,
  getUserWorkoutsForMonth,
  getUserWorkoutsForDay,
};
