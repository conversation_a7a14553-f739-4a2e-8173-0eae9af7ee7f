const Joi = require('joi');
const { objectId, validateSpecialChar } = require('./custom.validation');

const notificationSchema = {
    title: Joi.string(),
    description: Joi.string(),
    isForAll: Joi.boolean(),
    scheduledAt: Joi.date(),
    isScheduled: Joi.boolean(),
    TimezoneOffset: Joi.number(),
};


const AddNotification = {
    body: Joi.object().keys({
        ...notificationSchema,
    }),
};

const query = {
    query: Joi.object().keys({
      search: Joi.string().optional().custom(validateSpecialChar),
    }),
};

module.exports = {
    AddNotification,
    query
};
