const express = require('express');
const router = express.Router();
const { firebaseAuth } = require('../../middlewares/firebaseAuth');
// const validate = require('../../../middlewares/validate');
// const { adminProtect } = require('../../../middlewares/adminAuth');
// const equipmentValidation = require('../../../validations/equipment.validation');

const favouriteController = require('../../controllers/favourite.controller');

router.post(
    '/addAndRemove',
    firebaseAuth,
    favouriteController.favourite
);

router.get(
    '/',
    firebaseAuth,
    favouriteController.getfavourites
);

module.exports = router;
